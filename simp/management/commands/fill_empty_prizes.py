from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from simp.models import Draw, Prize, PrizeTemplate


class Command(BaseCommand):
    help = 'Fill out a SIMP draw with empty prizes up to the total pool size'

    def add_arguments(self, parser):
        parser.add_argument('draw_id', type=int, help='ID of the draw to fill with empty prizes')

    def handle(self, *args, **options):
        draw_id = options['draw_id']
        
        try:
            draw = Draw.objects.get(pk=draw_id)
        except Draw.DoesNotExist:
            raise CommandError(f'Draw with ID {draw_id} does not exist')
        
        # Find an empty prize template
        empty_template = PrizeTemplate.objects.filter(is_empty=True).first()
        if not empty_template:
            raise CommandError('No empty prize template found. Create a prize template with is_empty=True first.')
        
        # Count existing prizes
        existing_prize_count = Prize.objects.filter(draw=draw).count()
        total_pool_size = draw.total_pool_size
        
        # Calculate how many empty prizes to create
        prizes_to_create = total_pool_size - existing_prize_count
        
        if prizes_to_create <= 0:
            self.stdout.write(self.style.SUCCESS(f'Draw {draw.name} already has {existing_prize_count} prizes, which meets or exceeds the total pool size of {total_pool_size}. No new prizes created.'))
            return
        
        self.stdout.write(f'Creating {prizes_to_create} empty prizes for draw {draw.name}...')
        
        # Create the empty prizes in a transaction
        with transaction.atomic():
            prizes = [
                Prize(
                    draw=draw,
                    prize_template=empty_template,
                )
                for _ in range(prizes_to_create)
            ]
            Prize.objects.bulk_create(prizes)
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {prizes_to_create} empty prizes for draw {draw.name}'))
        self.stdout.write(self.style.SUCCESS(f'Draw now has {existing_prize_count + prizes_to_create} prizes out of a total pool size of {total_pool_size}'))
