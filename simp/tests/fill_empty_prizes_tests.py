from io import StringIO
from django.core.management import call_command
from django.core.management.base import CommandError
from django.utils import timezone
from core.test_utils import NexusTestCase
from core.tests.factories import ClientFactory
from simp.models import Draw, Prize, PrizeTemplate


class FillEmptyPrizesCommandTest(NexusTestCase):
    def setUp(self):
        super().setUp()
        self.client_obj = ClientFactory()
        
        # Create a draw
        self.draw = Draw.objects.create(
            name="Test Draw",
            client=self.client_obj,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timezone.timedelta(days=7),
            base_pool_size=100,
            new_subscriber_buffer=0,
            respin_buffer=0,
            status=Draw.DrawStatus.DRAFT
        )
        
        # Create prize templates
        self.empty_template = PrizeTemplate.objects.create(
            name="Empty Prize",
            description="An empty prize",
            is_empty=True
        )
        
        self.real_template = PrizeTemplate.objects.create(
            name="Real Prize",
            description="A real prize",
            is_empty=False
        )
        
        # Create some initial prizes
        for i in range(10):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.real_template
            )
    
    def test_command_creates_empty_prizes(self):
        """Test that the command creates the correct number of empty prizes"""
        out = StringIO()
        call_command('fill_empty_prizes', self.draw.id, stdout=out)
        
        # Check that we now have 100 prizes (10 real + 90 empty)
        self.assertEqual(Prize.objects.filter(draw=self.draw).count(), 100)
        self.assertEqual(Prize.objects.filter(draw=self.draw, prize_template=self.empty_template).count(), 90)
        
        # Check the output
        output = out.getvalue()
        self.assertIn(f'Successfully created 90 empty prizes for draw {self.draw.name}', output)
    
    def test_command_with_nonexistent_draw(self):
        """Test that the command raises an error for a nonexistent draw"""
        with self.assertRaises(CommandError):
            call_command('fill_empty_prizes', 999)
    
    def test_command_with_no_empty_template(self):
        """Test that the command raises an error if there's no empty template"""
        # Delete the empty template
        self.empty_template.delete()
        
        with self.assertRaises(CommandError):
            call_command('fill_empty_prizes', self.draw.id)
    
    def test_command_with_full_draw(self):
        """Test that the command doesn't create prizes if the draw is already full"""
        # Create enough prizes to fill the draw
        for i in range(90):
            Prize.objects.create(
                draw=self.draw,
                prize_template=self.real_template
            )
        
        out = StringIO()
        call_command('fill_empty_prizes', self.draw.id, stdout=out)
        
        # Check that we still have 100 prizes
        self.assertEqual(Prize.objects.filter(draw=self.draw).count(), 100)
        
        # Check the output
        output = out.getvalue()
        self.assertIn(f'Draw {self.draw.name} already has 100 prizes', output)
