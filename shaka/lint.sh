#!/bin/bash -e
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
cd "$SCRIPT_DIR"

DJANGO_PROJECTS=("cdr_db" "nexus" "client_dashboard")

for PROJECT in "${DJANGO_PROJECTS[@]}"; do
    cd "$SCRIPT_DIR/lambda/shaka/$PROJECT"
    pylint --load-plugins pylint_django --django-settings-module="$PROJECT.settings" --recursive=y --ignore=.venv,.nodeenv,.terraform,node_modules --disable=C0301,C0114,C0116,C0115,R1705,no-else-raise,global-statement,unnecessary-lambda-assignment,duplicate-code,too-many-ancestors,too-few-public-methods,too-many-lines,no-else-break,invalid-name ./
    cd "$SCRIPT_DIR"
done

LAMBDA_PROJECTS=("fetch_transatel_sftp_files" "run_airtable_import" "fetch_gamma_sftp_files" "async_api")

for LAMBDA_PROJECT in "${LAMBDA_PROJECTS[@]}"; do
    cd "$SCRIPT_DIR/lambda/shaka/$LAMBDA_PROJECT"
    pylint --recursive=y --ignore=.venv,.nodeenv,.terraform,node_modules --disable=C0301,C0114,C0116,C0115,R1705,no-else-raise,global-statement,unnecessary-lambda-assignment,duplicate-code,too-many-ancestors,too-few-public-methods,too-many-lines,no-else-break,invalid-name ./
    cd "$SCRIPT_DIR"
done

cd lambda/shaka/client_dashboard_frontend
yarn run lint
