resource "aws_elastic_beanstalk_application" "staging_staging_eb_app" {
  name = "staging-staging-eb-app"
}

resource "aws_cloudwatch_log_group" "staging_staging_eb_log_group" {
  name              = "/aws/elasticbeanstalk/staging-staging-eb-env/django"
  retention_in_days = 7
}

resource "aws_elastic_beanstalk_environment" "staging_staging_eb_env" {
  name                = "staging-staging-eb-env"
  application         = aws_elastic_beanstalk_application.staging_staging_eb_app.name
  solution_stack_name = "64bit Amazon Linux 2023 v4.0.6 running Python 3.11"

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DJANGO_SETTINGS_MODULE"
    value     = "nexus.staging_staging_settings"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_USER"
    value     = local.staging_creds["DB_USER"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_PASSWORD"
    value     = local.staging_creds["DB_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_HOST"
    value     = aws_db_instance.staging_staging.address
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_NAME"
    value     = aws_db_instance.staging_staging.db_name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "CDR_DB_API_TOKEN"
    value     = "nope"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LAMBDA_API_TOKEN"
    value     = local.staging_creds["LAMBDA_API_TOKEN"]
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GAMMA_WEBHOOK_SECRET_KEY"
    value     = local.staging_creds["GAMMA_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }


  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = "t2.small"
    resource  = ""
  }
  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.staging_eb_profile.name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:python"
    name      = "WSGIPath"
    value     = "nexus.wsgi:application"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "ListenerEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = "arn:aws:acm:eu-west-2:************:certificate/b4446da7-854e-4a4e-b249-5164a27e8c61"
    resource  = ""
  }
}


# https://epam.github.io/edp-install/operator-guide/waf-tf-configuration/
resource "aws_wafv2_regex_pattern_set" "staging_staging_host_regex" {
  name  = "staging-staging-host-regex"
  scope = "REGIONAL"

  regular_expression {
    regex_string = "staging.shaka.tel"
  }
}

resource "aws_wafv2_web_acl" "staging_staging_host_acl" {
  name  = "staging-staging-host-acl"
  scope = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    override_action {
      none {}
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "PreventHostInjections"
    priority = 0

    statement {
      regex_pattern_set_reference_statement {
        arn = aws_wafv2_regex_pattern_set.staging_staging_host_regex.arn

        field_to_match {
          single_header {
            name = "host"
          }
        }

        text_transformation {
          priority = 0
          type     = "NONE"
        }
      }
    }

    action {
      allow {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "PreventHostInjections"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "staging-staging-host-acl"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "staging_waf_staging_alb" {
  resource_arn = aws_elastic_beanstalk_environment.staging_staging_eb_env.load_balancers[0]
  web_acl_arn  = aws_wafv2_web_acl.staging_staging_host_acl.arn
}



resource "aws_db_instance" "staging_staging" {
  allocated_storage   = 10
  db_name             = "staging"
  engine              = "postgres"
  engine_version      = "15.8"
  instance_class      = "db.t3.small"
  username            = local.staging_creds["DB_USER"]
  password            = local.staging_creds["DB_PASSWORD"]
  identifier          = "staging-staging"
  deletion_protection = true
  publicly_accessible = true
  lifecycle {
    prevent_destroy = true
  }
  backup_retention_period = 7
}
