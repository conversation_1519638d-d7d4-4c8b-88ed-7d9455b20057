resource "aws_db_instance" "staging_test_db" {
  allocated_storage   = 10
  db_name             = "staging_test_db"
  engine              = "postgres"
  engine_version      = "15.8"
  instance_class      = "db.t3.micro"
  username            = local.test_db_creds["DB_USER"]
  password            = local.test_db_creds["DB_PASSWORD"]
  identifier          = "staging-test-db"
  publicly_accessible = true
  apply_immediately   = true
}


// todo - I added public security group access for now, need to restrict to codebuild
