resource "aws_iam_policy" "staging_eb_deploy_pipeline_policy" {
  name        = "staging-eb-deploy-pipeline-policy"
  description = "Policy to allow codepipeline to deploy to elastic beanstalk"
  policy      = <<EOF
{
    "Statement": [
        {
            "Action": [
                "iam:PassRole"
            ],
            "Resource": "*",
            "Effect": "Allow",
            "Condition": {
                "StringEqualsIfExists": {
                    "iam:PassedToService": [
                        "cloudformation.amazonaws.com",
                        "elasticbeanstalk.amazonaws.com",
                        "ec2.amazonaws.com",
                        "ecs-tasks.amazonaws.com"
                    ]
                }
            }
        },
        {
            "Action": [
              "logs:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codedeploy:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "ec2:*",
                "autoscaling:*",
                "cloudwatch:*",
                "cloudformation:*",
                "lambda:*",
                "s3:*",
                "elasticbeanstalk:*",
                "elasticloadbalancing:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "cloudformation:CreateStack",
                "cloudformation:DeleteStack",
                "cloudformation:DescribeStacks",
                "cloudformation:UpdateStack",
                "cloudformation:CreateChangeSet",
                "cloudformation:DeleteChangeSet",
                "cloudformation:DescribeChangeSet",
                "cloudformation:ExecuteChangeSet",
                "cloudformation:SetStackPolicy",
                "cloudformation:ValidateTemplate"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codebuild:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Action": [
                "codepipeline:*"
            ],
            "Resource": "*",
            "Effect": "Allow"
        },
        {
            "Effect": "Allow",
            "Action": [
              "codestar-notifications:*",
              "codestar-connections:*"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "appconfig:StartDeployment",
                "appconfig:StopDeployment",
                "appconfig:GetDeployment"
            ],
            "Resource": "*"
        }
    ],
    "Version": "2012-10-17"
}
EOF
}

resource "aws_iam_role" "staging_eb_deploy_pipeline_role" {
  name               = "staging-eb-deploy-pipeline-role"
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Effect": "Allow"
    },
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "staging_eb_deploy_pipeline_attach" {
  role       = aws_iam_role.staging_eb_deploy_pipeline_role.name
  policy_arn = aws_iam_policy.staging_eb_deploy_pipeline_policy.arn
}


resource "aws_codepipeline" "staging_eb_deploy_pipeline" {
  for_each = var.elastic_beanstalks
  name     = "staging-eb-deploy-pipeline-${each.key}"
  role_arn = aws_iam_role.staging_eb_deploy_pipeline_role.arn

  artifact_store {
    location = aws_s3_bucket.staging_terraform_pipeline_artifact_store.bucket
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "Source"
      category         = "Source"
      owner            = "AWS"
      provider         = "S3"
      version          = "1"
      output_artifacts = ["source_output"]
      namespace        = "Source"

      configuration = {
        S3Bucket             = aws_s3_bucket.staging_terraform_pipeline_artifact_store.bucket
        S3ObjectKey          = "eb-builds/staging/${each.key}.zip"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Deploy"
    action {
      name            = "Deploy"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]

      configuration = {
        ProjectName = aws_codebuild_project.staging_meta_eb_deploy_codebuild[each.key].name
      }
    }
  }
}

// https://repost.aws/questions/QUiKyt_-5-SdW2Gia9ICH7dA/canary-deployments-for-lambda-using-codedeploy-with-codepipeline

resource "aws_codebuild_project" "staging_meta_eb_deploy_codebuild" {
  for_each     = var.elastic_beanstalks
  name         = "staging-meta-eb-deploy-codebuild-${each.key}"
  service_role = aws_iam_role.staging_eb_deploy_pipeline_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  environment {
    compute_type = "BUILD_GENERAL1_SMALL"
    image        = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
    type         = "LINUX_CONTAINER"
  }
  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
    }
  }
  source {
    type      = "CODEPIPELINE"
    buildspec = <<EOF
version: 0.2

phases:
  build:
    commands:
      - "version_label=`date +%s`"
      - "aws elasticbeanstalk create-application-version --application-name ${each.value.application_name} --version-label \"$version_label\" --source-bundle S3Bucket=\"${aws_s3_bucket.staging_terraform_pipeline_artifact_store.bucket}\",S3Key=eb-builds/staging/${each.key}.zip"
      - "aws elasticbeanstalk update-environment --environment-name ${each.value.environment_name} --version-label \"$version_label\""
      - "aws elasticbeanstalk wait environment-updated --environment-name ${each.value.environment_name} --application-name ${each.value.application_name}"
      - "sleep 1"
    EOF
  }
}


resource "aws_acm_certificate" "staging_eb_certs" {
  for_each          = var.elastic_beanstalks
  provider          = aws.use1
  domain_name       = "${each.value.domain_name}.shaka.tel"
  validation_method = "DNS"
}

resource "aws_cloudfront_distribution" "staging_eb_cloudfront_distro" {
  for_each = var.elastic_beanstalks
  origin {
    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "https-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2"]
    }
    domain_name = "elb.${each.value.domain_name}.shaka.tel"
    origin_id   = "staging-origin-${each.key}"
  }

  enabled             = true
  is_ipv6_enabled     = true
  comment             = "Managed by Terraform, for ${each.key} EB"
  default_root_object = each.value.default_root_object

  aliases = [lookup(each.value, "cloudfront_domain_alias", "${each.value.domain_name}.shaka.tel")]

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "staging-origin-${each.key}"

    forwarded_values {
      headers      = ["*"]
      query_string = true

      cookies {
        forward = "all"
      }
    }

    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
    viewer_protocol_policy = "redirect-to-https"
  }

  price_class = "PriceClass_100"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    acm_certificate_arn      = lookup(each.value, "cloudfront_cert_arn", aws_acm_certificate.staging_eb_certs[each.key].arn)
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1"
  }


}


resource "aws_route53_record" "staging_eb_dns" {
  for_each        = var.elastic_beanstalks
  allow_overwrite = true
  name            = each.value.domain_name
  type            = "A"
  zone_id         = "Z00399212MJ5MNUCH698X"
  alias {
    name                   = aws_cloudfront_distribution.staging_eb_cloudfront_distro[each.key].domain_name
    zone_id                = aws_cloudfront_distribution.staging_eb_cloudfront_distro[each.key].hosted_zone_id
    evaluate_target_health = true
  }
}

locals {
  eb_validations = flatten([for eb_name, eb_details in var.elastic_beanstalks : [for dvo in aws_acm_certificate.staging_eb_certs[eb_name].domain_validation_options : {
    name   = dvo.resource_record_name
    record = dvo.resource_record_value
    type   = dvo.resource_record_type
    d_name = dvo.domain_name
    }

  ]])
}

resource "aws_route53_record" "staging_client_eb_cert_validations" {
  for_each        = { for x in local.eb_validations : x.d_name => x }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = "Z00399212MJ5MNUCH698X"
}

resource "aws_acm_certificate_validation" "staging_eb_validation" {
  for_each                = var.elastic_beanstalks
  certificate_arn         = aws_acm_certificate.staging_eb_certs[each.key].arn
  validation_record_fqdns = [for record in aws_route53_record.staging_client_eb_cert_validations : record.fqdn]
  provider                = aws.use1
}



resource "aws_acm_certificate" "staging_eb_elb_certs" {
  for_each          = var.elastic_beanstalks
  domain_name       = "elb.${each.value.domain_name}.shaka.tel"
  validation_method = "DNS"
  provider          = aws
}

resource "aws_route53_record" "staging_eb_elb_dns" {
  for_each        = var.elastic_beanstalks
  allow_overwrite = true
  name            = "elb.${each.value.domain_name}"
  type            = "A"
  zone_id         = "Z00399212MJ5MNUCH698X"
  alias {
    name                   = each.value.lb_domain
    zone_id                = each.value.lb_zone_id
    evaluate_target_health = true
  }
}

locals {
  eb_elb_validations = flatten([for eb_name, eb_details in var.elastic_beanstalks : [for dvo in aws_acm_certificate.staging_eb_elb_certs[eb_name].domain_validation_options : {
    name   = dvo.resource_record_name
    record = dvo.resource_record_value
    type   = dvo.resource_record_type
    d_name = dvo.domain_name
    }

  ]])
}

resource "aws_route53_record" "staging_client_eb_elb_cert_validations" {
  for_each        = { for x in local.eb_elb_validations : x.d_name => x }
  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = "Z00399212MJ5MNUCH698X"
}

resource "aws_acm_certificate_validation" "staging_eb_elb_validation" {
  for_each                = var.elastic_beanstalks
  certificate_arn         = aws_acm_certificate.staging_eb_elb_certs[each.key].arn
  validation_record_fqdns = [for record in aws_route53_record.staging_client_eb_elb_cert_validations : record.fqdn]
  provider                = aws
}
