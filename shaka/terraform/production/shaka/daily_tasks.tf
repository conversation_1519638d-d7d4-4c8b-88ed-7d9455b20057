data "aws_iam_policy_document" "prod_daily_tasks_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_daily_tasks_policy" {
  name        = "prod-daily-tasks-policy"
  path        = "/"
  description = "IAM policy for the dangerous users"
  policy      = data.aws_iam_policy_document.prod_daily_tasks_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_daily_tasks_attach" {
  role       = aws_iam_role.prod_daily_tasks_role.name
  policy_arn = aws_iam_policy.prod_daily_tasks_policy.arn
}

resource "aws_iam_role" "prod_daily_tasks_role" {
  name = "prod_daily_tasks_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_daily_tasks" {
  function_name                  = "prod-daily-tasks"
  role                           = aws_iam_role.prod_daily_tasks_role.arn
  handler                        = "lambda.daily_tasks.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      SIMP_POINTS_ENDPOINT = "https://simpnexus.shaka.tel/backend-api/daily-tasks/"
      SIMP_API_TOKEN       = local.simpnexus_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_daily_tasks_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_daily_tasks.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}


resource "aws_iam_role" "prod_daily_tasks_schedule_role" {
  name = "prod_daily_tasks_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_daily_tasks_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_daily_tasks_schedule_policy" {
  name        = "prod-daily-tasks-schedule-policy"
  path        = "/"
  description = "IAM policy for the daily tasks"
  policy      = data.aws_iam_policy_document.prod_daily_tasks_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_daily_tasks_scedule_attach" {
  role       = aws_iam_role.prod_daily_tasks_schedule_role.name
  policy_arn = aws_iam_policy.prod_daily_tasks_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_daily_tasks_schedule" {
  name       = "prod-daily-tasks-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every day"
  schedule_expression = "cron(0 3 * * ? *)"
  target {
    arn      = aws_lambda_function.prod_daily_tasks.arn
    role_arn = aws_iam_role.prod_daily_tasks_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }

}
