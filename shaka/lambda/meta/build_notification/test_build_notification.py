import os
import unittest
from unittest import mock

import requests_mock

from .build_notification import lambda_handler


class TestBuildNotification(unittest.TestCase):
    @mock.patch.dict(os.environ, {'SLACK_WEBHOOK': 'https://test-slack-webhook'})
    def test_posts_to_slack(self):
        sample_message = {
            'Records': [{
                "Type" : "Notification",
                "MessageId" : "ec79e122-3b6f-5f79-8b46-bcf27e1ec485",
                "TopicArn" : "arn:aws:sns:eu-west-2:************:prod-pipeline-notifications",
                "Message" : "{\"account\":\"************\",\"detailType\":\"CodePipeline Stage Execution State Change\",\"region\":\"eu-west-2\",\"source\":\"aws.codepipeline\",\"time\":\"2023-11-16T16:28:27Z\",\"notificationRuleArn\":\"arn:aws:codestar-notifications:eu-west-2:************:notificationrule/fa9f10ea8e1f02df43bbe6ef5997c3c562776e53\",\"detail\":{\"pipeline\":\"prod-terraform-pipeline\",\"execution-id\":\"643b09b8-a688-4306-bad7-73d7614ec84a\",\"start-time\":\"2023-11-16T16:27:55.453Z\",\"stage\":\"TerraformLint\",\"state\":\"FAILED\",\"version\":4.0,\"pipeline-execution-attempt\":0.0},\"resources\":[\"arn:aws:codepipeline:eu-west-2:************:prod-terraform-pipeline\"],\"additionalAttributes\":{\"failedActionCount\":1,\"failedActions\":[{\"action\":\"TerraformLint\",\"additionalInformation\":\"Build terminated with state: FAILED\"}]}}",
                "Timestamp" : "2023-11-16T16:28:33.507Z",
                "SignatureVersion" : "1",
                "Signature" : "NWwmyE5kfOhDkO4vpy5bXao8Vo+TUxBQNEZgPnlV2tPCL80adqmJbqTP/hv16Nt8CnH4jniUyEs6joaUdsODF8PzqE/IlAYVd7GzjRn5hxIN/SBIeRF2sIllcYMaa8FI/u4VhP9m3gCiT6jqhEqgXlQ1/At1ce2eV/sADe+BkeF06kjUnIqG0YjlmXC39uK47sXSa3FHd2aCHwb2iJLLh2NDoTBaArNa8hA5/jgK2qR8vE2f+7eAvtxup3yE4+HLken8IQlC1gFgVOP2LRVUYsO7j8GWwpKfkFbzNrIzPpzkeAwD2V9284hwGvePiObv7A+MKgdkWoqBy3rTbGtHAw==",
                "SigningCertURL" : "https://sns.eu-west-2.amazonaws.com/SimpleNotificationService-01d088a6f77103d0fe307c0069e40ed6.pem",
                "UnsubscribeURL" : "https://sns.eu-west-2.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-2:************:prod-pipeline-notifications:9d1a4672-df51-4f60-9e61-a4e92dfd18bd"
            }]
        }
        with requests_mock.Mocker() as mock_req:
            expected = mock_req.post(os.environ['SLACK_WEBHOOK'])
            lambda_handler(sample_message, None)
            self.assertEqual(1, expected.call_count)
