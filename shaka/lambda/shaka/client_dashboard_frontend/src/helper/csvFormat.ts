import { Subscriber } from 'src/types/subscribers';
import { currencyFormat } from '.';
import { DateTime } from 'luxon';

type CsvHelper<T> = Omit<T, 'id'>;

const convertToCsvFormat = <T extends object>(obj: T) => {
  return Object.keys(obj).reduce((acc, key) => {
    if (key === 'id') {
      return acc;
    }

    acc[key as keyof CsvHelper<T>] = `"${(
      obj[key as keyof CsvHelper<T>] as string
    )?.replaceAll('"', '""')}"` as T[keyof CsvHelper<T>];

    return acc;
  }, {} as CsvHelper<T>);
};

const csvFormatSubscribers = (subscribers: Subscriber[]) => {
  return subscribers?.map((subscriber) => {
    const validatedSubscriber = convertToCsvFormat<Subscriber>({
      ...subscriber,
      revenue_generated: currencyFormat(
        parseFloat(subscriber.revenue_generated)
      ),
      start_date: DateTime.fromISO(subscriber.start_date).toString()
    });

    return validatedSubscriber;
  });
};

export { csvFormatSubscribers };
