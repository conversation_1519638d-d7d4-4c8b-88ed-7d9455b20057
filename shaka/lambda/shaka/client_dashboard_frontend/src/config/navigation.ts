const URL_PREFIXES = {
  LOGIN: '/login',
  HUB: '/dashboard',
  MANAGE: '/manage',
  CUSTOMIZE: '/customize',
  SETTINGS: '/settings',
  ENGAGE: '/engage',
  MONETIZE: '/monetize'
};

export const ROUTES = {
  DASHBOARD: URL_PREFIXES.HUB,
  ENGAGE: URL_PREFIXES.ENGAGE,
  BEAMS: `${URL_PREFIXES.ENGAGE}/beams`,
  BEAMS_ADD: `${URL_PREFIXES.ENGAGE}/beams/add`,
  BEAMS_EDIT: `${URL_PREFIXES.ENGAGE}/beams/edit`,
  INSIGHTS: `${URL_PREFIXES.ENGAGE}/insights`,
  MONETIZE: URL_PREFIXES.MONETIZE,
  CAMPAIGNS: `${URL_PREFIXES.MONETIZE}/campaigns`,
  CAMPAIGNS_ADD: `${URL_PREFIXES.MONETIZE}/campaigns/add`,
  PERKS: `${URL_PREFIXES.MONETIZE}/perks`,
  PERKS_ADD: `${URL_PREFIXES.MONETIZE}/perks/add`,
  PERKS_EDIT: `${URL_PREFIXES.MONETIZE}/perks/edit`,
  SUBSCRIBERS: `${URL_PREFIXES.MANAGE}/subscribers`,
  ACTIVE_SUBSCRIBERS: `${URL_PREFIXES.MANAGE}/subscribers/active`,
  INACTIVE_SUBSCRIBERS: `${URL_PREFIXES.MANAGE}/subscribers/inactive`,
  PLANS: `${URL_PREFIXES.MANAGE}/plans`,
  PLANS_ADD: `${URL_PREFIXES.MANAGE}/plans/add`,
  BOLT_ONS: `${URL_PREFIXES.MANAGE}/plans?tab=bolt-ons`,
  BOLT_ON_ADD: `${URL_PREFIXES.MANAGE}/plans/bolt-on/add`,
  BOLT_ON_EDIT: `${URL_PREFIXES.MANAGE}/plans/bolt-on`,
  NETWORK_STATISTICS: `${URL_PREFIXES.MANAGE}/network_statistics`,
  BRANDING: `${URL_PREFIXES.CUSTOMIZE}/branding`,
  SPN: `${URL_PREFIXES.CUSTOMIZE}/spn`,
  VOICE_MAIL: `${URL_PREFIXES.CUSTOMIZE}/voicemail`,
  PROFILE: `${URL_PREFIXES.SETTINGS}/profile`,
  USERS: `${URL_PREFIXES.SETTINGS}/users`,
  USER_ADD: `${URL_PREFIXES.SETTINGS}/users/add`,
  CONFIG: `${URL_PREFIXES.SETTINGS}/config`,
  API: `${URL_PREFIXES.SETTINGS}/api`,
  LOGIN: URL_PREFIXES.LOGIN,
  FORGOT_PASSWORD: `${URL_PREFIXES.LOGIN}/forgot-password`
};

export const getPlanEditRoute = (id: string | number) =>
  `${ROUTES.PLANS}/${id}`;
export const getUserEditRoute = (id: string | number) =>
  `${ROUTES.USERS}/${id}`;
export const getSubscriberInfoRoute = (id: string | number) =>
  `${ROUTES.SUBSCRIBERS}/${id}`;
export const getBeamEditRoute = (id: string | number) =>
  `${ROUTES.BEAMS_EDIT}/${id}`;
export const getBeamDetailsRoute = (id: string | number) =>
  `${ROUTES.BEAMS}/${id}`;
export const getCampaignEditRoute = (id: string | number) =>
  `${ROUTES.CAMPAIGNS}/${id}`;
export const getPerksEditRoute = (id: string | number) =>
  `${ROUTES.PERKS_EDIT}/${id}`;
export const getPerksViewRoute = (id: string | number) =>
  `${ROUTES.PERKS}/${id}`;
export const getBoltOnEditRoute = (id: string | number) =>
  `${ROUTES.BOLT_ON_EDIT}/${id}`;
export const getBoltonsViewRoute = () => `${ROUTES.PLANS}?tab=bolt-ons`;

export const navbarConfig = [
  {
    id: 'dashboard',
    title: 'Hub',
    path: ROUTES.DASHBOARD
  },
  {
    id: 'manage',
    title: 'Manage',
    path: ROUTES.SUBSCRIBERS
  },
  {
    id: 'customize',
    title: 'Customize',
    path: ROUTES.BRANDING
  },
  {
    id: 'engage',
    title: 'Engage',
    path: ROUTES.BEAMS
  },
  {
    id: 'monetize',
    title: 'Monetize',
    path: ROUTES.CAMPAIGNS
  }
];

export const disabledNavLinks =
  import.meta.env.VITE_ENABLE_DEMO_FEATURES === 'true'
    ? []
    : ['monetize', 'engage'];

export type SidebarItem = {
  id: string;
  title: string | JSX.Element;
  icon: string;
  path: string;
  children?: SidebarItem[];
};

export type SidebarConfig = {
  prefix: string;
  title?: string;
  nav?: SidebarItem[];
};

export const disabledSidebarItems = [
  'bolt',
  'voicemail',
  'config',
  'api',
  'insights',
  'export',
  'network-statistics'
];

export const sidebarConfigs: SidebarConfig[] = [
  { prefix: URL_PREFIXES.HUB, title: 'Hub' },
  {
    title: 'Manage',
    prefix: URL_PREFIXES.MANAGE,
    nav: [
      {
        id: 'subscribers',
        title: 'Subscribers',
        path: ROUTES.SUBSCRIBERS,
        icon: 'People',
        children: [
          {
            id: 'active-subscribers',
            title: 'Active Subscribers',
            icon: '',
            path: ROUTES.ACTIVE_SUBSCRIBERS
          },
          {
            id: 'inactive-subscribers',
            title: 'Inactive Subscribers',
            icon: '',
            path: ROUTES.INACTIVE_SUBSCRIBERS
          }
        ]
      },
      {
        id: 'plans',
        title: 'Plans & bolt-ons',
        path: ROUTES.PLANS,
        icon: 'PieChart'
      },
      {
        id: 'network-statistics',
        title: 'Network statistics',
        path: ROUTES.NETWORK_STATISTICS,
        icon: 'BarChart'
      }
    ]
  },
  {
    title: 'Monetize',
    prefix: URL_PREFIXES.MONETIZE,
    nav: [
      {
        id: 'campaigns',
        title: 'Campaigns',
        path: ROUTES.CAMPAIGNS,
        icon: 'Campaign'
      },
      {
        id: 'perks',
        title: 'Perks',
        path: ROUTES.PERKS,
        icon: 'Present'
      },
      {
        id: 'export',
        title: 'Export',
        path: ROUTES.MONETIZE,
        icon: 'Export'
      }
    ]
  },
  {
    title: 'Customize',
    prefix: URL_PREFIXES.CUSTOMIZE,
    nav: [
      {
        id: 'branding',
        title: 'Branding',
        path: ROUTES.BRANDING,
        icon: 'Branding'
      },
      {
        id: 'spn',
        title: 'Network Name',
        path: ROUTES.SPN,
        icon: 'Radar'
      },
      {
        id: 'voicemail',
        title: 'Voicemail',
        path: ROUTES.VOICE_MAIL,
        icon: 'Phone'
      }
    ]
  },
  {
    title: 'Engage',
    prefix: URL_PREFIXES.ENGAGE,
    nav: [
      {
        id: 'beams',
        title: 'Beams',
        path: ROUTES.BEAMS,
        icon: 'Sms'
      },
      {
        id: 'insights',
        title: 'Location insights',
        path: ROUTES.INSIGHTS,
        icon: 'Navigation'
      }
    ]
  },
  {
    title: 'Settings',
    prefix: URL_PREFIXES.SETTINGS,
    nav: [
      {
        id: 'profile',
        title: 'Profile',
        path: ROUTES.PROFILE,
        icon: 'Profile'
      },
      {
        id: 'users',
        title: 'Users',
        path: ROUTES.USERS,
        icon: 'People'
      },
      {
        id: 'config',
        title: 'Config',
        path: ROUTES.CONFIG,
        icon: 'Spanner'
      },
      {
        id: 'api',
        title: 'API',
        path: ROUTES.API,
        icon: 'Code'
      }
    ]
  }
];
