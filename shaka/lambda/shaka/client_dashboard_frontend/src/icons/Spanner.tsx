type Props = {
  fill?: string;
};

export default function SpannerIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M5.67607 8.84629C3.90029 8.84629 2.45557 7.40156 2.45557 5.62578C2.45557 5.11123 2.57988 4.59951 2.81514 4.1458C2.87881 4.02324 3.02969 3.97539 3.15215 4.03887C3.2748 4.10244 3.32256 4.25332 3.25908 4.37588C3.05771 4.76426 2.95557 5.18477 2.95557 5.62578C2.95557 7.12588 4.17598 8.34629 5.67607 8.34629C7.17607 8.34629 8.39648 7.12588 8.39648 5.62578C8.39648 4.12568 7.17607 2.90527 5.67607 2.90527C5.27461 2.90527 4.88809 2.99063 4.52705 3.15908C4.40186 3.21738 4.25322 3.16328 4.19482 3.03818C4.13643 2.91309 4.19053 2.76436 4.31572 2.70596C4.74336 2.50645 5.20107 2.40527 5.67607 2.40527C7.45186 2.40527 8.89648 3.85 8.89648 5.62578C8.89648 7.40156 7.45186 8.84629 5.67607 8.84629Z"
        fill={fill}
      />
      <path
        d="M14.7933 17.5948C13.2765 17.5948 12.0425 16.3608 12.0425 14.844C12.0425 13.3272 13.2765 12.093 14.7933 12.093C16.3101 12.093 17.544 13.3272 17.544 14.844C17.544 15.1387 17.4978 15.4286 17.4065 15.7053C17.3632 15.8362 17.2214 15.9067 17.0907 15.8645C16.9597 15.8211 16.8884 15.6797 16.9315 15.5487C17.0062 15.3225 17.044 15.0854 17.044 14.844C17.044 13.6028 16.0343 12.593 14.7933 12.593C13.5522 12.593 12.5425 13.6028 12.5425 14.844C12.5425 16.085 13.5522 17.0948 14.7933 17.0948C15.0452 17.0948 15.2925 17.0536 15.5276 16.9723C15.6595 16.9268 15.8007 16.9965 15.8456 17.127C15.8907 17.2575 15.8214 17.3999 15.6909 17.445C15.403 17.5444 15.1009 17.5948 14.7933 17.5948Z"
        fill={fill}
      />
      <path
        d="M12.4305 14.2729C12.3664 14.2729 12.3025 14.2485 12.2537 14.1997L6.61133 8.55713C6.51367 8.45947 6.51367 8.30117 6.61133 8.20361C6.70898 8.10596 6.86719 8.10596 6.96484 8.20361L12.6072 13.8462C12.7049 13.9438 12.7049 14.1021 12.6072 14.1997C12.5584 14.2485 12.4945 14.2729 12.4305 14.2729Z"
        fill={fill}
      />
      <path
        d="M14.3918 12.593C14.3277 12.593 14.2639 12.5686 14.215 12.5197L8.34668 6.65137C8.24902 6.55371 8.24902 6.39551 8.34668 6.29785C8.44434 6.2002 8.60254 6.2002 8.7002 6.29785L14.5686 12.1662C14.6662 12.2639 14.6662 12.4221 14.5686 12.5197C14.5197 12.5686 14.4559 12.593 14.3918 12.593Z"
        fill={fill}
      />
      <path
        d="M15.5811 17.3841C15.5178 17.3841 15.4547 17.3603 15.4061 17.3126L14.5066 16.4302C14.4428 16.3675 14.4172 16.2757 14.4396 16.1892L14.7543 14.9689C14.7766 14.8825 14.8434 14.8144 14.9293 14.7905L16.1434 14.453C16.2295 14.4284 16.3217 14.4529 16.3855 14.5154L17.285 15.398C17.3834 15.4948 17.385 15.653 17.2883 15.7517C17.1914 15.8501 17.0334 15.8515 16.9346 15.755L16.1383 14.9734L15.2023 15.2335L14.9598 16.1743L15.7561 16.9556C15.8547 17.0523 15.8562 17.2107 15.7596 17.3091C15.7105 17.3591 15.6459 17.3841 15.5811 17.3841Z"
        fill={fill}
      />
      <path
        d="M4.42698 6.54651C4.34729 6.54651 4.27043 6.50832 4.22268 6.44055L2.81379 4.44231C2.7342 4.32952 2.76116 4.17346 2.87405 4.09397C2.98684 4.01448 3.1428 4.04143 3.22239 4.15422L4.52502 6.00168L5.72766 5.60989L6.1051 4.42356L4.27756 3.13684C4.16467 3.05735 4.13762 2.90139 4.21711 2.7885C4.29651 2.67571 4.45266 2.64836 4.56545 2.72805L6.5428 4.12024C6.63196 4.18303 6.67014 4.29641 6.63713 4.40041L6.16536 5.88362C6.14094 5.96038 6.08108 6.02063 6.00452 6.04553L4.50442 6.5342C4.47893 6.5425 4.45286 6.54651 4.42698 6.54651Z"
        fill={fill}
      />
      <path
        d="M12.4309 12.3509C12.3668 12.3509 12.3029 12.3265 12.2541 12.2776L8.53369 8.55713C8.43604 8.45947 8.43604 8.30127 8.53369 8.20361C8.63135 8.10596 8.78955 8.10596 8.88721 8.20361L12.6076 11.9241C12.7053 12.0218 12.7053 12.18 12.6076 12.2776C12.5588 12.3265 12.4949 12.3509 12.4309 12.3509Z"
        fill={fill}
      />
    </svg>
  );
}
