type Props = {
  fill?: string;
};

export default function PhoneIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
    >
      <path
        d="M17.9579 13.5861C17.7481 13.3627 17.0076 12.7021 15.6463 11.8282C14.275 10.9467 13.2637 10.397 12.9736 10.2689C12.948 10.2575 12.9198 10.2534 12.8921 10.2568C12.8643 10.2603 12.8381 10.2713 12.8161 10.2885C12.3488 10.653 11.5621 11.3224 11.5199 11.3585C11.2475 11.592 11.2475 11.592 11.0245 11.5192C10.6323 11.3907 9.41406 10.7438 8.35251 9.67982C7.29097 8.61586 6.61073 7.36748 6.48215 6.97573C6.40862 6.75233 6.40862 6.75233 6.64287 6.47991C6.67903 6.43772 7.34883 5.65101 7.71325 5.18412C7.73054 5.16213 7.7415 5.13585 7.74497 5.1081C7.74844 5.08035 7.74428 5.05218 7.73294 5.02661C7.60477 4.73612 7.05511 3.7252 6.17357 2.35387C5.29846 0.992981 4.63871 0.252471 4.41531 0.0427329C4.39481 0.0233876 4.36947 0.00992989 4.34196 0.00377788C4.31446 -0.00237414 4.2858 -0.000992651 4.25901 0.00777662C3.47811 0.27613 2.72466 0.618546 2.00895 1.03035C1.31803 1.43199 0.663914 1.89384 0.0542122 2.41052C0.032929 2.42861 0.0169725 2.45216 0.00805373 2.47863C-0.000865008 2.5051 -0.00240944 2.53351 0.00358597 2.56079C0.0875614 2.95214 0.488956 4.58584 1.73452 6.84876C3.00541 9.15829 3.88614 10.3416 5.75249 12.2015C7.61883 14.0614 8.83949 14.9952 11.1514 16.2661C13.4143 17.5116 15.0489 17.9134 15.4394 17.9966C15.4667 18.0025 15.4952 18.001 15.5217 17.9921C15.5482 17.9831 15.5719 17.9672 15.5901 17.946C16.1067 17.3363 16.5684 16.6822 16.9698 15.9912C17.3816 15.2755 17.724 14.522 17.9924 13.7412C18.001 13.7146 18.0023 13.6862 17.9962 13.6589C17.9902 13.6317 17.9769 13.6065 17.9579 13.5861Z"
        fill={fill}
      />
    </svg>
  );
}
