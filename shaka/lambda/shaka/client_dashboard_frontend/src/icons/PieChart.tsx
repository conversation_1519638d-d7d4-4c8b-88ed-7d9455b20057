type Props = {
  fill?: string;
};

export default function PieChartIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="19"
      height="19"
      viewBox="0 0 19 19"
      fill="none"
    >
      <path
        d="M1.44888 13.7814C1.31792 13.7814 1.18973 13.7436 1.0798 13.6724C0.969868 13.6012 0.882887 13.4998 0.829356 13.3802C0.181617 11.9336 -0.0938099 10.3479 0.0281729 8.76755C0.150156 7.18722 0.66567 5.66254 1.52774 4.33245C2.38981 3.00236 3.57102 1.90914 4.96375 1.15241C6.35647 0.395689 7.91642 -0.000482543 9.50145 4.41085e-07C9.68139 4.41085e-07 9.85396 0.0714816 9.9812 0.198719C10.1084 0.325956 10.1799 0.498527 10.1799 0.678468V9.49855C10.1799 9.62956 10.142 9.75778 10.0708 9.86772C9.99952 9.97765 9.89795 10.0646 9.77835 10.1181L1.72578 13.7203C1.63884 13.76 1.54446 13.7808 1.44888 13.7814Z"
        fill={fill}
      />
      <path
        d="M11.9435 1.53411C11.8943 1.52414 11.8435 1.5252 11.7947 1.53723C11.746 1.54926 11.7006 1.57195 11.6617 1.60367C11.6228 1.63539 11.5914 1.67535 11.5698 1.72066C11.5482 1.76598 11.5369 1.81552 11.5368 1.86571V9.49848C11.5365 9.89131 11.4226 10.2757 11.209 10.6053C10.9953 10.935 10.691 11.1959 10.3325 11.3566L3.01907 14.6294C2.97373 14.6496 2.9334 14.6796 2.9009 14.7171C2.8684 14.7546 2.84454 14.7988 2.83099 14.8466C2.81744 14.8943 2.81454 14.9445 2.8225 14.9935C2.83045 15.0425 2.84906 15.0892 2.87701 15.1302C3.43671 15.9534 4.13146 16.6761 4.93192 17.2678C6.4485 18.396 8.28968 19.0027 10.1799 18.997C15.0432 18.997 19 15.0403 19 10.1769C19 5.91744 15.9647 2.35336 11.9435 1.53411Z"
        fill={fill}
      />
    </svg>
  );
}
