type Props = {
  fill?: string;
};

export default function PeopleIcon({ fill = '#9698AB' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="16"
      viewBox="0 0 22 16"
      fill="none"
    >
      <path
        d="M14.5454 7.99999C13.6108 7.99999 12.7072 7.58272 11.9999 6.82545C11.3122 6.08681 10.8922 5.10181 10.8181 4.05272C10.739 2.93363 11.0804 1.90454 11.779 1.15454C12.4776 0.404545 13.4545 0 14.5454 0C15.6285 0 16.6081 0.411818 17.3045 1.16C18.0076 1.91545 18.3499 2.94272 18.2708 4.05227C18.1949 5.10272 17.7754 6.08727 17.089 6.82499C16.3835 7.58272 15.4804 7.99999 14.5454 7.99999Z"
        fill={fill}
      />
      <path
        d="M14.5454 7.99999C13.6108 7.99999 12.7072 7.58272 11.9999 6.82545C11.3122 6.08681 10.8922 5.10181 10.8181 4.05272C10.739 2.93363 11.0804 1.90454 11.779 1.15454C12.4776 0.404545 13.4545 0 14.5454 0C15.6285 0 16.6081 0.411818 17.3045 1.16C18.0076 1.91545 18.3499 2.94272 18.2708 4.05227C18.1949 5.10272 17.7754 6.08727 17.089 6.82499C16.3835 7.58272 15.4804 7.99999 14.5454 7.99999Z"
        fill="#2E215C"
        fillOpacity="0.2"
      />
      <path
        d="M20.5376 16H8.55356C8.36088 16.0011 8.17054 15.9579 7.99717 15.8738C7.8238 15.7897 7.67204 15.667 7.55356 15.515C7.42788 15.3503 7.34109 15.1593 7.29968 14.9563C7.25828 14.7533 7.26333 14.5435 7.31447 14.3428C7.69719 12.806 8.64538 11.5314 10.0563 10.6573C11.3086 9.88186 12.9026 9.45459 14.5454 9.45459C16.2204 9.45459 17.7726 9.86368 19.0322 10.6387C20.4463 11.5082 21.3958 12.79 21.7767 14.3455C21.8272 14.5464 21.8317 14.7562 21.7899 14.9591C21.748 15.162 21.6608 15.3528 21.5349 15.5173C21.4165 15.6685 21.2651 15.7907 21.0923 15.8744C20.9194 15.958 20.7297 16.001 20.5376 16Z"
        fill={fill}
      />
      <path
        d="M20.5376 16H8.55356C8.36088 16.0011 8.17054 15.9579 7.99717 15.8738C7.8238 15.7897 7.67204 15.667 7.55356 15.515C7.42788 15.3503 7.34109 15.1593 7.29968 14.9563C7.25828 14.7533 7.26333 14.5435 7.31447 14.3428C7.69719 12.806 8.64538 11.5314 10.0563 10.6573C11.3086 9.88186 12.9026 9.45459 14.5454 9.45459C16.2204 9.45459 17.7726 9.86368 19.0322 10.6387C20.4463 11.5082 21.3958 12.79 21.7767 14.3455C21.8272 14.5464 21.8317 14.7562 21.7899 14.9591C21.748 15.162 21.6608 15.3528 21.5349 15.5173C21.4165 15.6685 21.2651 15.7907 21.0923 15.8744C20.9194 15.958 20.7297 16.001 20.5376 16Z"
        fill="#2E215C"
        fillOpacity="0.2"
      />
      <path
        d="M5.9545 8.18186C4.35495 8.18186 2.94859 6.69458 2.81814 4.86686C2.75359 3.9305 3.04541 3.06459 3.63632 2.42959C4.22086 1.80095 5.04541 1.45459 5.9545 1.45459C6.86359 1.45459 7.68177 1.80277 8.26949 2.43504C8.86495 3.07504 9.15586 3.93913 9.08768 4.86777C8.95722 6.69504 7.55131 8.18186 5.9545 8.18186Z"
        fill={fill}
      />
      <path
        d="M5.9545 8.18186C4.35495 8.18186 2.94859 6.69458 2.81814 4.86686C2.75359 3.9305 3.04541 3.06459 3.63632 2.42959C4.22086 1.80095 5.04541 1.45459 5.9545 1.45459C6.86359 1.45459 7.68177 1.80277 8.26949 2.43504C8.86495 3.07504 9.15586 3.93913 9.08768 4.86777C8.95722 6.69504 7.55131 8.18186 5.9545 8.18186Z"
        fill="#2E215C"
        fillOpacity="0.2"
      />
      <path
        d="M8.93903 9.61127C8.13948 9.22036 7.10176 9.0249 5.95494 9.0249C4.61585 9.0249 3.3154 9.37399 2.29267 10.0076C1.13313 10.7272 0.35313 11.7749 0.0381301 13.0399C-0.00796754 13.2219 -0.0123276 13.4119 0.0253762 13.5957C0.0630799 13.7796 0.141871 13.9526 0.255857 14.1017C0.364015 14.2406 0.502577 14.3528 0.660887 14.4297C0.819197 14.5066 0.993037 14.5462 1.16904 14.5454H6.21449C6.29965 14.5453 6.3821 14.5154 6.44747 14.4609C6.51285 14.4063 6.55699 14.3305 6.57221 14.2467C6.57721 14.2181 6.58358 14.1894 6.59085 14.1613C6.9763 12.6131 7.87949 11.3049 9.21403 10.3513C9.26311 10.3159 9.30259 10.2688 9.32889 10.2143C9.35519 10.1598 9.36747 10.0996 9.36463 10.0392C9.36178 9.97875 9.3439 9.91997 9.3126 9.86819C9.28131 9.8164 9.23758 9.77325 9.18539 9.74263C9.11403 9.70081 9.03221 9.65672 8.93903 9.61127Z"
        fill={fill}
      />
      <path
        d="M8.93903 9.61127C8.13948 9.22036 7.10176 9.0249 5.95494 9.0249C4.61585 9.0249 3.3154 9.37399 2.29267 10.0076C1.13313 10.7272 0.35313 11.7749 0.0381301 13.0399C-0.00796754 13.2219 -0.0123276 13.4119 0.0253762 13.5957C0.0630799 13.7796 0.141871 13.9526 0.255857 14.1017C0.364015 14.2406 0.502577 14.3528 0.660887 14.4297C0.819197 14.5066 0.993037 14.5462 1.16904 14.5454H6.21449C6.29965 14.5453 6.3821 14.5154 6.44747 14.4609C6.51285 14.4063 6.55699 14.3305 6.57221 14.2467C6.57721 14.2181 6.58358 14.1894 6.59085 14.1613C6.9763 12.6131 7.87949 11.3049 9.21403 10.3513C9.26311 10.3159 9.30259 10.2688 9.32889 10.2143C9.35519 10.1598 9.36747 10.0996 9.36463 10.0392C9.36178 9.97875 9.3439 9.91997 9.3126 9.86819C9.28131 9.8164 9.23758 9.77325 9.18539 9.74263C9.11403 9.70081 9.03221 9.65672 8.93903 9.61127Z"
        fill="#2E215C"
        fillOpacity="0.2"
      />
    </svg>
  );
}
