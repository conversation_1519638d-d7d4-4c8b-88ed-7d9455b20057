type Props = {
  fill?: string;
};

export default function NavigationIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1292_452)">
        <g opacity="0.5">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 0C4.47716 0 0 4.47716 0 10C0 15.5229 4.47716 20 10 20C15.5229 20 20 15.5229 20 10C20 4.47716 15.5229 0 10 0ZM13.7422 6.25789C13.9335 6.44919 14.0003 6.73217 13.9147 6.98883L11.2805 14.8916C11.1923 15.1561 10.9581 15.3453 10.6808 15.3757C10.4035 15.4061 10.1339 15.2723 9.99036 15.0331L8.10656 11.8935L4.96689 10.0097C4.72769 9.86616 4.59393 9.59653 4.62437 9.31924C4.6548 9.04196 4.84387 8.80777 5.10851 8.71956L13.0112 6.08533C13.2679 5.99977 13.5508 6.06657 13.7422 6.25789Z"
            fill="black"
            fillOpacity="0.21"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 0C4.47716 0 0 4.47716 0 10C0 15.5229 4.47716 20 10 20C15.5229 20 20 15.5229 20 10C20 4.47716 15.5229 0 10 0ZM13.7422 6.25789C13.9335 6.44919 14.0003 6.73217 13.9147 6.98883L11.2805 14.8916C11.1923 15.1561 10.9581 15.3453 10.6808 15.3757C10.4035 15.4061 10.1339 15.2723 9.99036 15.0331L8.10656 11.8935L4.96689 10.0097C4.72769 9.86616 4.59393 9.59653 4.62437 9.31924C4.6548 9.04196 4.84387 8.80777 5.10851 8.71956L13.0112 6.08533C13.2679 5.99977 13.5508 6.06657 13.7422 6.25789Z"
            fill={fill}
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_1292_452">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
