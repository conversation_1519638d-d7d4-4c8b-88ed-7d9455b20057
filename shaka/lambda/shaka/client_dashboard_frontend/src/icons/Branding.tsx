type Props = {
  fill?: string;
};

export default function BrandingIcon({ fill = '#9E9EA8' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <g clipPath="url(#clip0_656_3079)">
        <path
          d="M15.7422 4.2577C14.9889 3.50227 14.0939 2.90292 13.1086 2.49397C12.1232 2.08502 11.0668 1.87451 10 1.87451C8.93317 1.87451 7.87681 2.08502 6.89146 2.49397C5.90611 2.90292 5.01114 3.50227 4.25782 4.2577C3.5024 5.01102 2.90304 5.90599 2.49409 6.89134C2.08514 7.87668 1.87463 8.93304 1.87463 9.99988C1.87463 11.0667 2.08514 12.1231 2.49409 13.1084C2.90304 14.0938 3.5024 14.9888 4.25782 15.7421C5.01114 16.4975 5.90611 17.0969 6.89146 17.5058C7.87681 17.9148 8.93317 18.1253 10 18.1253C11.0668 18.1253 12.1232 17.9148 13.1086 17.5058C14.0939 17.0969 14.9889 16.4975 15.7422 15.7421C16.4976 14.9888 17.097 14.0938 17.5059 13.1084C17.9149 12.1231 18.1254 11.0667 18.1254 9.99988C18.1254 8.93304 17.9149 7.87668 17.5059 6.89134C17.097 5.90599 16.4976 5.01102 15.7422 4.2577ZM10 13.3514C9.69308 13.0006 9.29391 12.7429 8.84785 12.6075C8.40178 12.4722 7.9267 12.4647 7.47657 12.5858L8.67188 9.99988H11.3281L12.5234 12.5858C12.0733 12.4647 11.5982 12.4722 11.1522 12.6075C10.7061 12.7429 10.3069 13.0006 10 13.3514ZM13.125 16.1249C12.3443 16.5196 11.4961 16.7635 10.625 16.8436V14.9999C10.625 14.6684 10.7567 14.3504 10.9911 14.116C11.2255 13.8816 11.5435 13.7499 11.875 13.7499C12.2065 13.7499 12.5245 13.8816 12.7589 14.116C12.9933 14.3504 13.125 14.6684 13.125 14.9999V16.1249ZM14.8594 14.8593C14.7031 15.0155 14.5391 15.1639 14.375 15.3046V13.7499C14.3727 13.6588 14.3542 13.5688 14.3203 13.4843L10.5703 5.35926H10.5625C10.5525 5.33427 10.5394 5.31065 10.5234 5.28895C10.5234 5.28113 10.5234 5.28113 10.5156 5.27332L10.4766 5.21864H10.4688C10.4525 5.19849 10.4342 5.18016 10.4141 5.16395H10.3984L10.3438 5.11707H10.3281L10.2656 5.07801H10.2578L10.1875 5.05457H10.1641L10.0938 5.03895H9.90626L9.82813 5.05457H9.81251L9.75001 5.07801H9.73438L9.66407 5.10926H9.65626L9.60157 5.15613H9.58594L9.53126 5.21082H9.52344L9.48438 5.26551L9.46876 5.28113C9.45874 5.30612 9.44562 5.32974 9.42969 5.35145L7.70313 9.10145L5.67969 13.4843C5.6458 13.5688 5.62728 13.6588 5.62501 13.7499V15.3046C5.46094 15.1639 5.29688 15.0155 5.14063 14.8593C4.17858 13.898 3.52325 12.673 3.25752 11.3392C2.99179 10.0055 3.12761 8.62283 3.6478 7.36626C4.16799 6.1097 5.04917 5.03564 6.17988 4.27995C7.31059 3.52427 8.64002 3.12092 10 3.12092C11.36 3.12092 12.6894 3.52427 13.8201 4.27995C14.9508 5.03564 15.832 6.1097 16.3522 7.36626C16.8724 8.62283 17.0082 10.0055 16.7425 11.3392C16.4768 12.673 15.8214 13.898 14.8594 14.8593Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_656_3079">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
