import { createColumnHelper } from '@tanstack/react-table';
import Table from 'src/components/Table';
import { currencyFormat } from 'src/helper';
import clsx from 'clsx';

export const tableColumns = [
  {
    id: 'name',
    label: 'Name',
    cellStyles: 'text-left',
    headStyles: 'text-left'
  },
  {
    id: 'active_subscribers',
    label: 'Active Subscribers'
  },
  {
    id: 'profit',
    label: 'Profit'
  }
];

type Statement = {
  name: string;
  active_subscribers: number;
  profit: number;
};

interface Props {
  data: Statement[];
  isPredicted?: boolean;
}

const columnHelper = createColumnHelper<Statement>();
const columns = (isPredicted?: boolean) =>
  tableColumns.map(({ id, label, cellStyles, headStyles }) =>
    columnHelper.accessor(id as keyof Statement, {
      size: id !== 'profit' ? 250 : undefined,
      cell: (info) => {
        switch (id) {
          case 'profit':
            return <div>{currencyFormat(Number(info.getValue()))}</div>;

          default:
            return <div className={clsx(cellStyles)}>{info.getValue()}</div>;
        }
      },
      header: () => {
        if (id === 'profit' && isPredicted) {
          return <div className={clsx(headStyles)}>Predicted {label}</div>;
        }

        return <div className={clsx(headStyles)}>{label}</div>;
      }
    })
  );

export default function StatementsTable({ data, isPredicted }: Props) {
  return <Table columns={columns(isPredicted)} data={data} />;
}
