import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  ColumnDef,
  Row,
  PaginationState,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState
} from '@tanstack/react-table';
import clsx from 'clsx';
import { useMemo, useState } from 'react';
import Typography, { TypographyShade } from './Typography';
import { Popover } from '@headlessui/react';
import ArrowsUpDownIcon from 'src/icons/ArrowsUpDown';
import ArrowDownIcon from 'src/icons/ArrowDown';

type Props<T> = {
  columns: ColumnDef<T, any>[];
  data: T[];
  squareRow?: boolean;
  onRowClick?: (row: Row<T>) => void;
  withPagination?: boolean;
  withSorting?: boolean;
  pageSize?: number;
};

const headClass = 'relative border-b border-white/20 text-xl h-16 uppercase';
const headerCellClass = 'text-white/80';
const rowClass = 'group h-14 border-b border-white/5 relative';
const rowClickStyles =
  'group-hover:bg-white/5 first:rounded-l-xl first:pl-4 last:rounded-r-xl last:pr-4';
const rowClickStylesSquare = 'group-hover:bg-white/5';
const cellClass = 'text-center text-lg';

export default function Table<T>({
  data,
  columns,
  squareRow,
  onRowClick,
  pageSize,
  withPagination,
  withSorting
}: Props<T>) {
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: pageSize || 10
  });
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(withPagination && {
      getPaginationRowModel: getPaginationRowModel(),
      onPaginationChange: setPagination,
      state: {
        pagination,
        sorting
      }
    }),
    ...(withSorting && {
      getSortedRowModel: getSortedRowModel(),
      onSortingChange: setSorting
    })
  });

  const currentPage = pagination.pageIndex || 0;
  const pagesBeforeCurrent = currentPage - 2 > 0 ? currentPage - 2 : 0;
  const pagesAfterCurrent = currentPage - 2 > 0 ? currentPage + 2 : 4;
  const showEndPages = currentPage < table.getPageCount() - 3;

  const paginationButtons = useMemo(() => {
    if (!withPagination) return null;

    const pages = [];
    const pagesAmount = data.length / (pageSize || 10);
    for (let i = 0; i < pagesAmount; i++) {
      const isActive = pagination.pageIndex === i;

      pages.push(
        <button
          key={i}
          className={clsx(
            'inline-block w-9 h-9 rounded-full p-1',
            isActive
              ? 'bg-white/20 text-white'
              : 'text-white/80 hover:bg-white/10'
          )}
          onClick={() => table.setPageIndex(Number(i))}
          disabled={isActive}
        >
          <Typography shade={isActive ? undefined : TypographyShade.Light}>
            {i + 1}
          </Typography>
        </button>
      );
    }

    return pages;
  }, [pagination, table, data, currentPage]);

  return (
    <>
      <table className="w-full table">
        <thead className={headClass}>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr
              key={headerGroup.id}
              className={clsx('relative table-header-row', headerCellClass)}
            >
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="font-normal"
                  style={{ width: `${header.getSize()}px` }}
                >
                  {header.isPlaceholder ? null : (
                    <div
                      {...(withSorting && {
                        className:
                          'cursor-pointer select-none flex items-center gap-1 justify-center',

                        title:
                          header.column.getNextSortingOrder() === 'asc'
                            ? 'Sort ascending'
                            : header.column.getNextSortingOrder() === 'desc'
                              ? 'Sort descending'
                              : 'Clear sort'
                      })}
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {withSorting &&
                        ({
                          asc: <ArrowDownIcon className="w-4 h-4" />,
                          desc: <ArrowDownIcon className="w-4 h-4 rotate-180" />
                        }[header.column.getIsSorted() as string] ??
                          null)}

                      {withSorting && !header.column.getIsSorted() ? (
                        <ArrowsUpDownIcon className="w-4 h-4 opacity-50" />
                      ) : null}
                    </div>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.id}
              className={clsx(
                'table-row',
                rowClass,
                onRowClick && 'cursor-pointer'
              )}
              onClick={() => onRowClick && onRowClick(row)}
            >
              {row.getVisibleCells().map((cell) => (
                <td
                  key={cell.id}
                  className={clsx(
                    cellClass,
                    onRowClick &&
                      (squareRow ? rowClickStylesSquare : rowClickStyles)
                  )}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {paginationButtons && paginationButtons?.length > 1 && (
        <div className="flex justify-center gap-4 mt-8">
          {paginationButtons?.length < 10 ? (
            paginationButtons
          ) : (
            <>
              <button
                className="inline-block rounded-full p-1 px-3.5 text-white/80 hover:bg-white/10"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                {'<'}
              </button>
              {paginationButtons.slice(pagesBeforeCurrent, pagesAfterCurrent)}
              <Popover className="relative">
                <Popover.Button className="w-9 h-9 rounded-full p-1 text-white/80 hover:bg-white/10">
                  <span>...</span>
                </Popover.Button>

                <Popover.Panel className="absolute z-10 top-10">
                  <span className="flex items-center justify-center gap-1 -translate-x-1/2 py-2 px-4 bg-black/10 w-[180px] rounded-2xl">
                    <p>Go to page:</p>
                    <input
                      type="number"
                      min="1"
                      max={table.getPageCount()}
                      defaultValue={table.getState().pagination.pageIndex + 1}
                      onChange={(e) => {
                        const page = e.target.value
                          ? Number(e.target.value) - 1
                          : 0;
                        table.setPageIndex(page);
                      }}
                      className="rounded-xl bg-grey-input p-1 text-center focus:outline-none"
                    />
                  </span>
                </Popover.Panel>
              </Popover>

              {showEndPages && paginationButtons.slice(-2)}
              <button
                className="inline-block rounded-full p-1 px-3.5 text-white/80 hover:bg-white/10"
                onClick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
              >
                {'>'}
              </button>
            </>
          )}
        </div>
      )}
    </>
  );
}
