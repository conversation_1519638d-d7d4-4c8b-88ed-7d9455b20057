import { Popover, Transition } from '@headlessui/react';

import { Fragment, ReactNode } from 'react';
import clsx from 'clsx';

type Props = {
  label: string;
  classes?: {
    label?: string;
    button?: string;
  };
  icon?: ReactNode;
  children: ReactNode;
};

export default function Example({ label, classes, icon, children }: Props) {
  return (
    <Popover className="relative">
      {() => (
        <>
          <Popover.Button
            className={clsx(
              'group inline-flex justify-center items-center rounded-sm bg-orange-700 px-3 py-2 text-sm font-medium',
              classes?.button
            )}
          >
            {icon && icon}
            <span>{label}</span>
          </Popover.Button>
          <Transition
            as={Fragment}
            enter="transition ease-out duration-200"
            enterFrom="opacity-0 translate-y-1"
            enterTo="opacity-100 translate-y-0"
            leave="transition ease-in duration-150"
            leaveFrom="opacity-100 translate-y-0"
            leaveTo="opacity-0 translate-y-1"
          >
            <Popover.Panel className="absolute left-1/2 z-10 mt-3 -translate-x-1/2 transform">
              {children}
            </Popover.Panel>
          </Transition>
        </>
      )}
    </Popover>
  );
}
