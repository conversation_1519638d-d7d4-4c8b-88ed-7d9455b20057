import clsx from 'clsx';
import Typography, { TypographySize } from './Typography';

interface Props {
  status: string;
  color: StatusColor;
  size?: StatusSize;
}

export enum StatusColor {
  Success = 'bg-success',
  Error = 'bg-error',
  Default = 'bg-gray-100',
  Warning = 'bg-warning'
}

export enum StatusSize {
  Small = 'small',
  Big = 'big'
}

export default function Status({
  status,
  color = StatusColor.Default,
  size = StatusSize.Small
}: Props) {
  return (
    <span className="inline-flex gap-2 items-center">
      <span
        className={clsx(
          'inline-block  rounded-full',
          color,
          size === StatusSize.Big ? 'w-2.5 h-2.5' : 'w-2 h-2'
        )}
      />
      <Typography
        size={
          size === StatusSize.Big
            ? TypographySize.BodyS
            : TypographySize.Caption
        }
      >
        {status}
      </Typography>
    </span>
  );
}
