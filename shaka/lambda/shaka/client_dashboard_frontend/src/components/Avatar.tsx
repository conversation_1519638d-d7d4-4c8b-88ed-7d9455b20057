type Props = {
  url?: string;
  initials?: string;
  size: number;
};

export default function Avatar({ url, size, initials }: Props) {
  if (!url) {
    return (
      <div
        className="inline-block rounded-full bg-blue-600 flex items-center justify-center text-white font-bold text-2xl"
        style={{ width: size, height: size }}
      >
        {initials}
      </div>
    );
  }

  return (
    <img
      className="inline-block rounded-full"
      width={size}
      height={size}
      src={url}
      alt=""
    />
  );
}
