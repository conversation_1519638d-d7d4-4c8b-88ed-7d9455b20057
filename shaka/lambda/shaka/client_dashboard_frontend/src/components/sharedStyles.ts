import clsx from 'clsx';
import { twMerge } from 'tailwind-merge';

export enum ButtonColor {
  Default = 'default',
  Light = 'light',
  Helper = 'helper',
  Rainbow = 'rainbow',
  Pink = 'pink',
  GrayGradient = 'gray-gradient',
  DarkAction = 'dark-action'
}

const mainButtonStyle =
  'rounded-md font-medium px-9 py-2 transition-colors text-center';

const defaultButtonStyles = clsx(
  mainButtonStyle,
  'bg-white/10 hover:bg-white/20 text-white'
);
const lightButtonStyles = twMerge(
  mainButtonStyle,
  'bg-white hover:bg-white/80 text-black '
);

const helperButtonStyles =
  'bg-white/10 hover:bg-white/20 text-white text-lg h-8 rounded-xl py-1 px-8 font-medium';

const rainbowButtonStyles = clsx(
  mainButtonStyle,
  'bg-gradient-button hover:opacity-90'
);

const pinkButtonStyles = clsx(
  mainButtonStyle,
  'bg-gradient-pink hover:bg-gradient-to-b hover:from-pink-200 hover:to-pink-200 transition-all'
);

const grayGradientStyle =
  'rounded-lg backdrop-blur-lg bg-white/10 h-10 pt-2 pr-4 pb-2 pl-4 font-medium transition-colors hover:bg-white/20';

const darkActionStyle =
  'bg-black/25 rounded-full px-8 py-1.5 hover:bg-black/50';

export const buttonStyles = {
  [ButtonColor.Default]: defaultButtonStyles,
  [ButtonColor.Light]: lightButtonStyles,
  [ButtonColor.Helper]: helperButtonStyles,
  [ButtonColor.Rainbow]: rainbowButtonStyles,
  [ButtonColor.Pink]: pinkButtonStyles,
  [ButtonColor.GrayGradient]: grayGradientStyle,
  [ButtonColor.DarkAction]: darkActionStyle
};

export const inputClass =
  'bg-grey-input text-white rounded-full focus:outline-none focus:ring focus:ring-primary block w-full px-7 py-2.5 text-xl';

export const textareaClass =
  'h-32 bg-grey-input text-white rounded-xl focus:outline-none focus:ring focus:ring-primary block w-full px-7 py-3 text-xl';

export const dropdownButtonClass =
  'relative bg-grey-input text-white w-full cursor-default py-2 pl-6 pr-8 text-left text-lg font-medium hover:cursor-pointer h-12';
export const dropdownOptionsClass =
  'absolute mt-1 max-h-60 w-full text-base overflow-auto rounded-md bg-grey-input-solid py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none z-10';
export const dropdownOptionClass =
  'relative cursor-default select-none py-2 px-4';
