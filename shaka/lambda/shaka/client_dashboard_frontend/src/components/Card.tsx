import { ReactNode } from 'react';
import clsx from 'clsx';
import Loader from './Loader';
import Typography, { TypographySize } from './Typography';

type Props = {
  title?: string;
  subtitle?: string;
  actions?: ReactNode;
  bottomActions?: ReactNode;
  children: ReactNode;
  classes?: string;
  isLoading?: boolean;
  fullHeight?: boolean;
  simple?: boolean;
};

export default function Card({
  title = '',
  actions,
  children,
  classes = '',
  isLoading,
  bottomActions,
  fullHeight,
  subtitle,
  simple
}: Props) {
  return (
    <>
      {!simple && title && (
        <div className="mb-5">
          <Typography size={TypographySize.Title}>{title}</Typography>
        </div>
      )}
      <div
        className={clsx(
          !simple && 'bg-gradient-card-3 bg-white/[0.03] rounded-2xl py-6 px-9',
          classes,
          fullHeight && 'h-full'
        )}
      >
        {(subtitle || actions) && (
          <div className="w-full h-20 flex justify-between pb-8 gap-4 items-center">
            {subtitle ? (
              <Typography size={TypographySize.Subtitle}>{subtitle}</Typography>
            ) : (
              <div />
            )}
            {!isLoading && actions}
          </div>
        )}
        {isLoading ? <Loader /> : children}
        <div className="flex justify-center items-center pt-10 gap-4">
          {bottomActions && !isLoading && bottomActions}
        </div>
      </div>
    </>
  );
}
