import { Link as RouterLink, LinkProps } from 'react-router-dom';
import { ButtonColor, buttonStyles } from './sharedStyles';
import clsx from 'clsx';

interface Props extends LinkProps {
  color?: ButtonColor;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

export default function Link({
  to,
  children,
  color = ButtonColor.Default,
  className,
  icon,
  fullWidth,
  ...props
}: Props) {
  const style = buttonStyles[color];

  return (
    <RouterLink
      className={clsx(
        style,
        className,
        icon && 'flex gap-4',
        fullWidth && 'w-full text-center'
      )}
      to={to}
      {...props}
    >
      {icon && <span className="-ml-1 mt-[1px]">{icon}</span>}
      {children}
    </RouterLink>
  );
}

export { ButtonColor as LinkColor } from './sharedStyles';
