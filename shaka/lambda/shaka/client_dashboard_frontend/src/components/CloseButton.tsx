import { CloseIcon } from 'src/icons/Close';
import { twMerge } from 'tailwind-merge';

export default function CloseButton({
  onClose,
  isLight
}: {
  onClose: () => void;
  isLight?: boolean;
}) {
  return (
    <button
      type="button"
      className={twMerge(
        'relative rounded-full p-1.5 hover:bg-white/10 focus-visible:outline-none',
        isLight && 'bg-white/20'
      )}
      onClick={onClose}
    >
      <CloseIcon />
    </button>
  );
}
