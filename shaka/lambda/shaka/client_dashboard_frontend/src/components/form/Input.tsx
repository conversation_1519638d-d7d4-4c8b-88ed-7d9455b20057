import clsx from 'clsx';
import { inputClass } from '../sharedStyles';
import { forwardRef, useState } from 'react';
import { EyeCrossedIcon } from 'src/icons/EyeCrossed';
import { EyeIcon } from 'src/icons/Eye';

type Props = {
  prefix?: string;
  shape?: 'rounded' | 'square';
} & React.InputHTMLAttributes<HTMLInputElement>;

const Input = forwardRef(function Input(
  { prefix, type, shape = 'rounded', ...props }: Props,
  ref: any
) {
  const isPasswordField = type === 'password';
  const [currentType, setCurrentType] = useState(type);

  const handleIconClick = () => {
    setCurrentType(currentType === 'password' ? 'text' : 'password');
  };

  return (
    <div className="relative">
      {prefix && (
        <div className="absolute pointer-events-none inset-y-0 left-0 flex items-center pl-7">
          <span className="text-gray-500 text-xl">{prefix}</span>
        </div>
      )}
      <input
        className={clsx(
          inputClass,
          prefix && 'pl-12',
          isPasswordField && 'pr-12',
          shape === 'rounded' ? 'rounded-full' : 'rounded-xl',
          props.disabled && 'opacity-50'
        )}
        type={currentType}
        ref={ref}
        {...props}
      />
      {isPasswordField && (
        <button
          type="button"
          className="absolute top-3 right-4 text-white/80 cursor-pointer hover:text-white/50 z-10 w-6 h-6"
          onClick={handleIconClick}
        >
          {currentType === 'password' ? <EyeCrossedIcon /> : <EyeIcon />}
        </button>
      )}
    </div>
  );
});

export default Input;
