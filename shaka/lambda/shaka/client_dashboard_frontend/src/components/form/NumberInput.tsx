import React from 'react';
import { inputClass } from '../sharedStyles';
import { twMerge } from 'tailwind-merge';

type Props = {
  prefix?: string;
  shape?: 'rounded' | 'square';
  integerOnly?: boolean;
  positiveOnly?: boolean;
  narrow?: boolean;
} & React.InputHTMLAttributes<HTMLInputElement>;

const NumberInput = React.forwardRef(function Input(
  {
    prefix,
    shape = 'rounded',
    integerOnly,
    positiveOnly = true,
    narrow,
    ...props
  }: Props,
  ref: any
) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (props.maxLength) {
      if (
        e.key === 'Backspace' ||
        e.key === 'Delete' ||
        e.key === 'ArrowLeft' ||
        e.key === 'ArrowRight' ||
        e.key === 'Tab'
      ) {
        return;
      }
      if (e.currentTarget.value.length >= props.maxLength) {
        e.preventDefault();
      }
    }

    if (integerOnly) {
      if (
        e.key === '.' ||
        e.key === ',' ||
        e.key === 'e' ||
        e.key === '-' ||
        e.key === '+'
      ) {
        e.preventDefault();
      }
    }

    if (positiveOnly && (e.key === '-' || e.key === '+')) {
      e.preventDefault();
    }
  };

  return (
    <div className="relative">
      {prefix && (
        <div className="absolute pointer-events-none inset-y-0 left-0 flex items-center pl-7">
          <span className="text-gray-500 text-xl">{prefix}</span>
        </div>
      )}
      <input
        className={twMerge(
          inputClass,
          narrow && 'px-5',
          prefix && 'pl-12',
          shape === 'rounded' ? 'rounded-full' : 'rounded-xl'
        )}
        ref={ref}
        type="number"
        onKeyDown={handleKeyDown}
        {...props}
      />
    </div>
  );
});

export default NumberInput;
