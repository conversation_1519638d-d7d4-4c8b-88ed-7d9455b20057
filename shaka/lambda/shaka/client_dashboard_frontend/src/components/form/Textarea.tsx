import React from 'react';
import { textareaClass } from '../sharedStyles';
import Typography, { TypographyShade, TypographySize } from '../Typography';
import {
  FieldValues,
  useController,
  UseControllerProps
} from 'react-hook-form';
import { getSMSUnits } from 'src/helper';

type Props<T extends FieldValues> =
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
    countChars?: boolean;
    height?: number;
  } & UseControllerProps<T>;

const Textarea = <T extends FieldValues>({
  name,
  control,
  countChars,
  height,
  ...props
}: Props<T>) => {
  const { field } = useController({
    name,
    control
  });
  const value = field.value as string;
  const textLength = value?.length || 0;

  return (
    <div className="relative">
      <textarea
        className={textareaClass}
        style={{
          ...(countChars && {
            borderBottom: '36px solid transparent'
          }),
          ...(height && {
            height: height + 'px'
          })
        }}
        {...field}
        {...props}
      />
      {countChars && (
        <span className="absolute right-3 bottom-1 w-full text-right">
          <Typography size={TypographySize.BodyS} shade={TypographyShade.Light}>
            {textLength} ({getSMSUnits(value) || 0} units)
          </Typography>
        </span>
      )}
    </div>
  );
};

export default Textarea;
