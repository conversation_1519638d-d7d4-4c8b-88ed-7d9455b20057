import clsx from 'clsx';
import React, { ReactNode } from 'react';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from '../Typography';
import Tooltip from '../Tooltip';
import { InfoBigIcon } from 'src/icons/InfoBig';

interface Props extends React.PropsWithChildren {
  label?: string;
  className?: string;
  subLabel?: ReactNode;
  direction?: 'column' | 'row';
  error?: string;
  description?: string;
  info?: string;
  optional?: boolean;
  postInputLabel?: ReactNode;
}

const LabeledInputWrapper = ({
  label,
  children,
  className = '',
  subLabel,
  error,
  direction = 'column',
  description,
  info,
  postInputLabel,
  optional
}: Props) => {
  return (
    <div
      className={clsx(
        'flex  gap-4 mb-4',
        direction === 'column' ? 'flex-col' : 'flex-row items-center',
        className
      )}
    >
      <div className="flex justify-between items-center">
        {label && (
          <Typography as="label">
            {label}
            {subLabel && <> {subLabel}</>}:{optional && ' (optional)'}
          </Typography>
        )}
        {info && (
          <div className="relative">
            <Tooltip
              text={info}
              colorScheme="flat-white"
              tooltipClassName="w-[250px]"
              textWrap
              near
            >
              <InfoBigIcon className="w-6 h-6" />
            </Tooltip>
          </div>
        )}
      </div>

      {postInputLabel ? (
        <div className="flex items-center gap-5">
          {children}
          <Typography shade={TypographyShade.Dark}>{postInputLabel}</Typography>
        </div>
      ) : (
        children
      )}

      {description && (
        <Typography size={TypographySize.Caption} shade={TypographyShade.Light}>
          {description}
        </Typography>
      )}
      {error && (
        <Typography color={TypographyColor.Error} size={TypographySize.Caption}>
          {error}
        </Typography>
      )}
    </div>
  );
};

export default LabeledInputWrapper;
