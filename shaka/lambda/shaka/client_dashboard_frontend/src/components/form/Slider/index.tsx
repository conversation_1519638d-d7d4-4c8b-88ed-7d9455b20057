import ReactSlider, { ReactSliderProps } from 'react-slider';
import getTrack from './getTrack';
import getMark from './getMark';
import getThumb from './getThumb';

interface Props extends Omit<ReactSliderProps, 'onChange'> {
  marks?: number[];
  onChange: (value: number) => void;
  format?: (value: number) => string;
  unlimited?: boolean;
  hideTooltip?: boolean;
}

const DEFAULT_MAX = 100;
const COEFF_MAX = 1.2;

export default function Slider({
  onChange,
  min,
  max,
  marks,
  step = 1,
  disabled,
  unlimited,
  hideTooltip,
  format = (v: number) => v.toString(),
  value,
  ...restProps
}: Props) {
  
  const handleChange = (value: number) => {
    onChange(value);
  };
  const maxPossible =
    max ||
    ((marks && marks[marks.length - 1]) || DEFAULT_MAX) *
      (unlimited ? COEFF_MAX : 1);

  return (
    <ReactSlider
      {...restProps}
      value={value || maxPossible}
      className="w-full h-4 mt-12 mb-12 z-[1]"
      min={min}
      max={maxPossible}
      disabled={disabled}
      marks={marks && [...marks, maxPossible]}
      onChange={handleChange}
      step={step}
      renderTrack={getTrack}
      renderThumb={getThumb(
        format,
        disabled,
        maxPossible,
        unlimited,
        hideTooltip
      )}
      renderMark={getMark(format, maxPossible, unlimited)}
    />
  );
}
