import React, { useEffect, useState } from 'react';
import { inputClass } from '../sharedStyles';
import { CalendarIcon } from '@heroicons/react/24/solid';
import { twMerge } from 'tailwind-merge';

type Props = React.InputHTMLAttributes<HTMLInputElement>;

const Calendar = React.forwardRef(function Calendar(
  { onFocus, placeholder, ...props }: Props,
  inputRef: any
) {
  const calendarRef = React.useRef<HTMLInputElement>(null);
  const [isPlaceholder, setIsPlaceholder] = useState(
    Boolean(placeholder && !props.value)
  );

  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsPlaceholder(false);
    if (calendarRef.current) {
      calendarRef.current.showPicker();
      onFocus?.(e);
    }
  };

  useEffect(() => {
    if (props.value) {
      setIsPlaceholder(false);
    }
  }),
    [props.value];

  return (
    <div className="relative cursor-pointer">
      <input
        type="datetime-local"
        ref={(e) => {
          inputRef(e);
          // @ts-expect-error it need to focus on the input
          calendarRef.current = e;
        }}
        className={twMerge(inputClass, isPlaceholder && 'text-transparent')}
        onFocus={handleFocus}
        {...props}
      />
      <span className="absolute right-5 top-3 pointer-events-none">
        <CalendarIcon className="w-6 h-6" />
      </span>

      {isPlaceholder && (
        <span
          className={twMerge(
            inputClass,
            'pointer-events-none absolute inset-0 bg-transparent'
          )}
        >
          {placeholder}
        </span>
      )}
    </div>
  );
});

export default Calendar;
