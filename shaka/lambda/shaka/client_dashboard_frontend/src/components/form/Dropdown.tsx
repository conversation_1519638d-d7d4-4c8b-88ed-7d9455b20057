import { Fragment } from 'react';
import { Listbox, Transition } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { useController } from 'react-hook-form';
import { twMerge } from 'tailwind-merge';

export type ListboxOption<T> = {
  label: string;
  value: T;
};

type Props<T> = {
  name: string;
  options: ListboxOption<T>[];
  classes?: {
    button?: string;
  };
  control: any;
  shape?: 'rounded' | 'square';
  disabled?: boolean;
};

const buttonClasses =
  'relative bg-grey-input text-white w-full cursor-default py-2 pl-6 pr-8 text-left text-lg font-medium hover:cursor-pointer h-12';
const optionsClasses =
  'absolute mt-1 max-h-60 w-full text-base overflow-auto rounded-md bg-grey-input-solid py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none z-10';
const optionClasses = 'relative cursor-default select-none py-2 px-4';

export default function Dropdown<T extends string | number>({
  name,
  options,
  classes,
  control,
  shape = 'rounded',
  disabled
}: Props<T>) {
  const { field } = useController({
    name,
    control
  });

  const selectedOption = options.find((option) => option.value === field.value);

  return (
    <Listbox value={field.value} onChange={field.onChange} disabled={disabled}>
      <div className="relative">
        <Listbox.Button
          className={twMerge(
            buttonClasses,
            classes?.button,
            shape === 'rounded' ? 'rounded-full' : 'rounded-xl',
            disabled && 'opacity-30'
          )}
          ref={field.ref}
        >
          <span className={twMerge('block truncate')}>
            {selectedOption?.label || 'Select option'}
          </span>
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
            <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
          </span>
        </Listbox.Button>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className={optionsClasses}>
            {options.map((option) => (
              <Listbox.Option
                key={option.value}
                className={({ active }) =>
                  twMerge(
                    optionClasses,
                    active ? 'bg-primary text-white' : 'text-white'
                  )
                }
                value={option.value}
              >
                {({ selected }) => (
                  <>
                    <span
                      className={`block truncate ${
                        selected ? 'font-bold' : 'font-normal'
                      }`}
                    >
                      {option.label}
                    </span>
                  </>
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
}
