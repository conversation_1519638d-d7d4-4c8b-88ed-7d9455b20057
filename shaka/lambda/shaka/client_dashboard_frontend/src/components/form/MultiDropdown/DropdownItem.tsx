import { Listbox, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import clsx from 'clsx';
import {
  dropdownButtonClass,
  dropdownOptionClass,
  dropdownOptionsClass
} from 'src/components/sharedStyles';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { ListboxOption } from '../Dropdown';
import { CrossIcon } from 'src/icons/Cross';
import { twMerge } from 'tailwind-merge';

export type ListOptionType = ListboxOption<string | number>;

export default function DropdownItem({
  onChange,
  onRemove,
  selectedOption,
  options,
  disabled,
  disableRemove
}: {
  selectedOption: ListOptionType;
  options: ListOptionType[];
  disabled?: boolean;
  disableRemove?: boolean;
  onChange: (value: ListOptionType) => void;
  onRemove: (value: ListOptionType) => void;
}) {
  return (
    <Listbox value={selectedOption} onChange={onChange}>
      <div className="relative">
        <Listbox.Button
          type="button"
          className={clsx(
            twMerge(dropdownButtonClass, 'pr-16'),
            disabled && 'pointer-events-none',
            'group rounded-xl'
          )}
        >
          <span className={clsx('block truncate')}>
            {selectedOption.label || 'Select option'}
          </span>
          {!disabled && (
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
            </span>
          )}
          {!disableRemove && (
            <span
              className={clsx(
                'pointer-events-auto absolute inset-y-1.5 p-2 items-center hover:bg-white/10 rounded-full cursor-pointer',
                disabled ? 'right-2' : 'right-6'
              )}
              onClick={() => onRemove(selectedOption)}
            >
              <CrossIcon className="h-5 w-5" />
            </span>
          )}
        </Listbox.Button>
        <Transition
          as={Fragment}
          leave="transition ease-in duration-100"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <Listbox.Options className={dropdownOptionsClass}>
            {options.map((option) => (
              <Listbox.Option
                key={option.value}
                className={({ active }) =>
                  clsx(
                    dropdownOptionClass,
                    active ? 'bg-primary text-white' : 'text-white'
                  )
                }
                value={option}
              >
                {({ selected }) => (
                  <>
                    <span
                      className={`block truncate ${
                        selected ? 'font-bold' : 'font-normal'
                      }`}
                    >
                      {option.label}
                    </span>
                  </>
                )}
              </Listbox.Option>
            ))}
          </Listbox.Options>
        </Transition>
      </div>
    </Listbox>
  );
}
