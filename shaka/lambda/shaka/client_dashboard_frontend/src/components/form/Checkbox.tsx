import clsx from 'clsx';
import { ChangeEvent } from 'react';

type Props = {
  name: string;
  label: string;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  checked?: boolean;
  disabled?: boolean;
};

export default function Checkbox({
  name,
  label,
  checked,
  onChange,
  disabled
}: Props) {
  return (
    <div
      className={clsx(
        'flex items-center',
        disabled && 'opacity-80 pointer-events-none'
      )}
    >
      <input
        name={name}
        id={name}
        type="checkbox"
        className={clsx(
          'light w-4 h-4 text-white rounded cursor-pointer',
          disabled && 'opacity-5'
        )}
        checked={checked}
        onChange={onChange}
      />
      <label
        className="ms-3 text-xl font-medium text-gray-300 cursor-pointer"
        htmlFor={name}
      >
        {label}
      </label>
    </div>
  );
}
