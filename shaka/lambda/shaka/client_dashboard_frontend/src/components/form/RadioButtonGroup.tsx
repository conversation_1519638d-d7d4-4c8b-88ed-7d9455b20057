import { RadioGroup } from '@headlessui/react';
import clsx from 'clsx';
import Typography, { TypographyColor, TypographySize } from '../Typography';
import Tooltip from '../Tooltip';

type Props = {
  options: { label: string; value: string }[];
  value: string;
  horizontal: boolean;
  onChange: (v: string) => void;
  disabledOptions?: string[];
  tooltipOptions?: Record<string, string>;
  shape?: 'rounded' | 'square';
};

const Option = ({
  rounded,
  option,
  disabled
}: {
  rounded: boolean;
  option: { label: string; value: string };
  disabled: boolean;
}) => {
  const optionStyles = rounded
    ? 'rounded-full px-10 py-2 min-w-[180px] max-2xl:min-w-0'
    : 'rounded-xl py-3 px-2.5 bg-grey-input';

  return (
    <RadioGroup.Option
      value={option.value}
      className={clsx(
        !rounded && 'w-full',
        disabled && 'opacity-50 pointer-events-none'
      )}
    >
      {({ checked }: { checked: boolean }) => (
        <div
          className={clsx(
            'cursor-pointer',
            'w-full text-sm font-medium leading-5 focus:outline-none  transition-[background-color] text-center',
            optionStyles,
            checked
              ? 'bg-white text-black shadow'
              : 'hover:bg-white/[0.12] hover:text-white'
          )}
        >
          <Typography
            as="label"
            size={TypographySize.BodyS}
            color={TypographyColor.Inherit}
            noWrap={rounded}
          >
            {option.label}
          </Typography>
        </div>
      )}
    </RadioGroup.Option>
  );
};

export default function RadioButtonGroup({
  options,
  value,
  horizontal,
  onChange,
  disabledOptions,
  tooltipOptions = {},
  shape = 'rounded'
}: Props) {
  const rounded = shape === 'rounded';
  const wrapperStyles = rounded
    ? 'gap-1 rounded-full p-1'
    : 'gap-4 rounded-xl p-5';

  return (
    <RadioGroup
      value={value}
      onChange={onChange}
      className={clsx('flex bg-grey-area', wrapperStyles, {
        'flex-col': !horizontal
      })}
    >
      {options.map((option) =>
        tooltipOptions[option.value] ? (
          <Tooltip
            key={option.value}
            text={tooltipOptions[option.value] || ''}
            hidden={!tooltipOptions[option.value]}
            colorScheme="flat-white"
            near
          >
            <Option
              rounded={rounded}
              option={option}
              disabled={!!disabledOptions?.includes(option.value)}
            />
          </Tooltip>
        ) : (
          <Option
            key={option.value}
            rounded={rounded}
            option={option}
            disabled={!!disabledOptions?.includes(option.value)}
          />
        )
      )}
    </RadioGroup>
  );
}
