import clsx from 'clsx';
import { useState } from 'react';
import Typography, { TypographyColor, TypographySize } from '../Typography';

interface Props {
  options: { label: string; value: string }[];
  shape?: 'rounded' | 'square';
}

export default function CheckboxGroup({ shape, options }: Props) {
  const rounded = shape === 'rounded';
  const wrapperStyles = rounded
    ? 'gap-1 rounded-full p-1'
    : 'gap-4 rounded-xl p-5';
  const optionStyles = rounded
    ? 'rounded-full px-10 py-2'
    : 'rounded-xl py-3 px-2.5 bg-grey-input text-center';

  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const handleCheckboxChange = (value: string) => {
    if (selectedItems.includes(value)) {
      setSelectedItems(selectedItems.filter((item) => item !== value));
    } else {
      setSelectedItems([...selectedItems, value]);
    }
  };

  return (
    <div className={clsx('flex bg-grey-area', wrapperStyles)}>
      {options.map((option) => (
        <label
          key={option.value}
          className={clsx(
            'w-full text-sm font-medium leading-5 focus:outline-none  transition-[background-color]',
            optionStyles,

            selectedItems.includes(option.value)
              ? 'bg-white text-black shadow'
              : 'hover:bg-white/[0.12] hover:text-white'
          )}
        >
          <input
            type="checkbox"
            value={option.value}
            checked={selectedItems.includes(option.value)}
            onChange={() => handleCheckboxChange(option.value)}
            className="size-0 absolute opacity-0"
          />
          <Typography
            size={TypographySize.BodyS}
            color={TypographyColor.Inherit}
            noWrap={rounded}
          >
            {option.label}
          </Typography>
        </label>
      ))}
    </div>
  );
}
