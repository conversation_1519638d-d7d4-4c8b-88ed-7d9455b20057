import { PropsWithChildren, useMemo } from 'react';
import Typography, { TypographyColor, TypographySize } from './Typography';

interface Props {
  error?: string;
  titles?: Record<string, string>;
}

export default function ErrorText({
  error: errorProp,
  children,
  titles
}: PropsWithChildren<Props>) {
  const error = errorProp || children;

  const flattenErrors = useMemo(() => {
    if (!error) return [];

    if (typeof error === 'object' && !Array.isArray(error)) {
      return Object.entries(error).map(([key, value]) => {
        if (!titles) return value;
        return `${titles[key as keyof typeof titles]}: ${value}`;
      });
    }

    return [error];
  }, [error]);

  if (!error) return null;

  return (
    <div className="mt-4">
      {flattenErrors.map((e) => (
        <div key={e}>
          <Typography
            color={TypographyColor.Error}
            size={TypographySize.Caption}
          >
            {e}
          </Typography>
        </div>
      ))}
    </div>
  );
}
