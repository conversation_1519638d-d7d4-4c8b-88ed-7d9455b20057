import clsx from 'clsx';

type Props = {
  title: string;
  titleColor: string;
  content: string;
};

export default function StatsCard({ title, titleColor, content }: Props) {
  return (
    <div className="bg-gradient-card-1 p-4 font-jakarta w-60 rounded-3xl">
      <div
        className={clsx(
          'text-xs tracking-[1px] uppercase font-medium',
          titleColor
        )}
      >
        {title}
      </div>
      <div className="mt-3 text-xl font-bold">{content}</div>
    </div>
  );
}
