import { ReactNode } from 'react';
import clsx from 'clsx';

type Props = {
  children: ReactNode;
  classes?: string;
  size?: 'small' | 'big';
  gradientSize?: 'big' | 'small';
};

const gradients = {
  big: 'radial-gradient(10% 20% at 80% -10%, #64418938 0%, #FFFFFF00 100%), radial-gradient(15% 46% at 98% 5%, #ffffff21 0%, #FFFFFF00 100%), radial-gradient(10% 45% at 75% 89%, #ef8df814 0%, #FFFFFF00 100%), rgba(255, 255, 255, 0.03)',
  small:
    'radial-gradient(10% 45% at 75% 15%, #ef8df814 0%, #FFFFFF00 100%), rgba(255, 255, 255, 0.03)'
};

export default function InfoCard({
  children,
  classes = '',
  size = 'big',
  gradientSize
}: Props) {
  const isBig = size === 'big';

  return (
    <div
      className={clsx(
        'relative bg-white/[0.03] rounded-2xl  flex flex-col',
        isBig && 'h-[280px] items-center p-3 justify-center',
        !isBig && 'h-[136px] p-7 justify-between',
        classes
      )}
      style={{
        ...(gradientSize && { background: gradients[gradientSize] })
      }}
    >
      {children}
    </div>
  );
}
