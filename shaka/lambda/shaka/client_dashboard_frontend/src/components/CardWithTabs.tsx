import { ReactNode, useEffect, useState } from 'react';
import clsx from 'clsx';
import Loader from './Loader';
import Typography, { TypographyShade, TypographySize } from './Typography';
import { Tab } from '@headlessui/react';
import { useSearchParams } from 'react-router-dom';

type TabWithLink = { title: string; tabName?: string };

type Props = {
  tabs: string[] | TabWithLink[];
  children: ReactNode;
  isLoading?: boolean;
  subtitle?: string;
  actions?: ReactNode;
};

export default function CardWithTabs({
  children,
  isLoading,
  tabs,
  actions,
  subtitle
}: Props) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentTab, setCurrentTab] = useState(0);

  useEffect(() => {
    const tab = searchParams.get('tab');

    if (tab) {
      const index = tabs.findIndex((t) =>
        typeof t === 'string' ? t === tab : t.tabName === tab
      );
      setCurrentTab(index);
      return;
    }

    setCurrentTab(0);
  }, [searchParams, tabs]);

  return (
    <Tab.Group onChange={setCurrentTab} selectedIndex={currentTab}>
      {tabs && (
        <Tab.List className="flex space-x-10 mb-5">
          {tabs.map((tab) => {
            const title = typeof tab === 'string' ? tab : tab.title;
            const tabName = typeof tab === 'string' ? undefined : tab.tabName;

            return (
              <Tab
                key={title}
                className="focus:outline-none"
                onClick={() => setSearchParams(tabName ? { tab: tabName } : {})}
              >
                {({ selected }) => (
                  <Typography
                    size={TypographySize.Title}
                    shade={selected ? undefined : TypographyShade.Light}
                    hoverEffect={!selected}
                  >
                    {title}
                  </Typography>
                )}
              </Tab>
            );
          })}
        </Tab.List>
      )}
      <div
        className={clsx(
          'bg-white/[0.03] bg-gradient-card-3',
          'rounded-2xl py-6 px-9'
        )}
      >
        {(subtitle || actions) && (
          <div className="w-full h-20 flex justify-between pb-8 gap-4">
            {subtitle ? <Typography>{subtitle}</Typography> : <div />}
            {!isLoading && actions}
          </div>
        )}
        {isLoading ? <Loader /> : children}
      </div>
    </Tab.Group>
  );
}
