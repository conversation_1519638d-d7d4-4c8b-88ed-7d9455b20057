import {
  flexRender,
  getCoreRowModel,
  useReactTable,
  ColumnDef,
  Row,
  SortingState,
  OnChangeFn
} from '@tanstack/react-table';
import clsx from 'clsx';
import { useMemo } from 'react';
import Typography, { TypographyShade } from './Typography';
import { Popover } from '@headlessui/react';
import ArrowsUpDownIcon from 'src/icons/ArrowsUpDown';
import ArrowDownIcon from 'src/icons/ArrowDown';

type Props<T> = {
  columns: ColumnDef<T, any>[];
  data: T[];
  withRowClick?: boolean;
  onRowClick?: (row: Row<T>) => void;
  withPagination?: boolean;
  withSorting?: boolean;
  pageSize?: number;
  pageCount: number;
  pageIndex: number;
  onPageChange?: (pageIndex: number) => void;
  sorting?: SortingState;
  onSortingChange?: OnChangeFn<SortingState>;
};

const headClass = 'relative border-b border-white/20 text-xl h-16 uppercase';
const headerCellClass = 'text-white/80';
const rowClass = 'group h-14 border-b border-white/5 relative';
const rowClickStyles =
  'group-hover:bg-white/5 first:rounded-l-xl first:pl-4 last:rounded-r-xl last:pr-4';
const cellClass = 'text-center text-lg';

export default function TableAsync<T>({
  data,
  columns,
  pageIndex,
  onPageChange,
  onRowClick,
  withRowClick,
  withPagination,
  withSorting,
  pageCount = 1,
  onSortingChange,
  sorting
}: Props<T>) {
  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    pageCount,
    ...(withSorting && {
      state: {
        sorting
      },
      onSortingChange,
      manualSorting: true
    })
  });

  const pagesBeforeCurrent = pageIndex - 2 > 0 ? pageIndex - 2 : 0;
  const pagesAfterCurrent = pageIndex - 2 > 0 ? pageIndex + 2 : 4;
  const showEndPages = pageIndex < pageCount - 3;

  const handlePageChange = (index: number) => {
    onPageChange && onPageChange(index);
  };

  const paginationButtons = useMemo(() => {
    if (!withPagination) return null;

    const pages = [];
    for (let i = 0; i < pageCount; i++) {
      const isActive = pageIndex === i;

      pages.push(
        <button
          key={i}
          className={clsx(
            'inline-block w-9 h-9 rounded-full p-1',
            isActive
              ? 'bg-white/20 text-white'
              : 'text-white/80 hover:bg-white/10'
          )}
          onClick={() => handlePageChange(i)}
          disabled={isActive}
        >
          <Typography shade={isActive ? undefined : TypographyShade.Light}>
            {i + 1}
          </Typography>
        </button>
      );
    }

    return pages;
  }, [pageIndex, table, data]);

  return (
    <>
      <table className="w-full table">
        <thead className={headClass}>
          {table.getHeaderGroups().map((headerGroup) => (
            <tr
              key={headerGroup.id}
              className={clsx('relative table-header-row', headerCellClass)}
            >
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="font-normal"
                  style={{ width: `${header.getSize()}px` }}
                >
                  {header.isPlaceholder ? null : (
                    <div
                      className={
                        header.column.getCanSort()
                          ? 'cursor-pointer select-none flex items-center gap-1 justify-center'
                          : ''
                      }
                      onClick={
                        header.column.getCanSort()
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                      title={
                        header.column.getCanSort()
                          ? header.column.getNextSortingOrder() === 'asc'
                            ? 'Sort ascending'
                            : header.column.getNextSortingOrder() === 'desc'
                              ? 'Sort descending'
                              : 'Clear sort'
                          : undefined
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}

                      {header.column.getCanSort() && (
                        <>
                          {{
                            asc: <ArrowDownIcon className="w-4 h-4" />,
                            desc: (
                              <ArrowDownIcon className="w-4 h-4 rotate-180" />
                            )
                          }[header.column.getIsSorted() as string] ?? null}

                          {header.column.getCanSort() &&
                          !header.column.getIsSorted() ? (
                            <ArrowsUpDownIcon className="w-4 h-4 opacity-50" />
                          ) : null}
                        </>
                      )}
                    </div>
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <tr
              key={row.id}
              className={clsx(
                'table-row',
                rowClass,
                withRowClick && 'cursor-pointer'
              )}
              onClick={() => onRowClick && onRowClick(row)}
            >
              {row.getVisibleCells().map((cell) => (
                <td
                  key={cell.id}
                  className={clsx(cellClass, withRowClick && rowClickStyles)}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {paginationButtons && paginationButtons?.length > 1 && (
        <div className="flex justify-center gap-4 mt-8">
          {paginationButtons?.length < 5 ? (
            paginationButtons
          ) : (
            <>
              {pageIndex !== 0 && (
                <button
                  className="inline-block rounded-full p-1 px-3.5 text-white/80 hover:bg-white/10"
                  onClick={() => handlePageChange(pageIndex - 1)}
                >
                  {'<'}
                </button>
              )}
              {paginationButtons.slice(pagesBeforeCurrent, pagesAfterCurrent)}
              <Popover className="relative">
                <Popover.Button className="w-9 h-9 rounded-full p-1 text-white/80 hover:bg-white/10">
                  <span>...</span>
                </Popover.Button>

                <Popover.Panel className="absolute z-10 top-10">
                  <span className="flex items-center justify-center gap-1 -translate-x-1/2 py-2 px-4 bg-black/10 w-[180px] rounded-2xl">
                    <p>Go to page:</p>
                    <input
                      type="number"
                      min="1"
                      max={pageCount}
                      defaultValue={pageIndex + 1}
                      onChange={(e) => {
                        const page = e.target.value
                          ? Number(e.target.value) - 1
                          : 0;
                        handlePageChange(page);
                      }}
                      className="rounded-xl bg-grey-input p-1 text-center focus:outline-none"
                    />
                  </span>
                </Popover.Panel>
              </Popover>

              {showEndPages && paginationButtons.slice(-2)}
              {pageIndex !== pageCount - 1 && (
                <button
                  className="inline-block rounded-full p-1 px-3.5 text-white/80 hover:bg-white/10"
                  onClick={() => handlePageChange(pageIndex + 1)}
                >
                  {'>'}
                </button>
              )}
            </>
          )}
        </div>
      )}
    </>
  );
}
