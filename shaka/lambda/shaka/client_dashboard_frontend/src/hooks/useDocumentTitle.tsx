import { useContext, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { ROUTES, sidebarConfigs } from 'src/config/navigation';
import { SpnContext } from 'src/contexts/SpnContext';

const loginPagesTitles = {
  [ROUTES.LOGIN]: 'Login',
  [ROUTES.FORGOT_PASSWORD]: 'Forgot Password'
};

const useDocumentTitle = () => {
  const { spn } = useContext(SpnContext);
  const { pathname } = useLocation();

  const pageName = useMemo(() => {
    const config = sidebarConfigs.find((item) => {
      return new RegExp('^' + item.prefix).test(pathname);
    });

    const pageName = config?.nav?.find((item) => {
      return new RegExp('^' + item.path).test(pathname);
    })?.title;

    return pageName || sidebarConfigs[0].title;
  }, [pathname]);

  useEffect(() => {
    const isLoginPage = Object.prototype.hasOwnProperty.call(
      loginPagesTitles,
      pathname
    );

    if (isLoginPage) {
      document.title = `${loginPagesTitles[pathname]} | shaka`;
      return;
    }

    document.title = `${pageName} - ${spn} | shaka`;
  }, [pathname, spn]);
};

export default useDocumentTitle;
