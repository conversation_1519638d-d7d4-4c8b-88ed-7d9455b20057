import { fetchPerks } from 'src/api/perks';
import { useAsync } from 'src/api/useApi';
import {
  usePerksStore,
  selectorPerksLoaded,
  setPerks,
  selectorPerks
} from 'src/store/perks';

export default function usePerks() {
  const perks = usePerksStore(selectorPerks);
  const isPerksLoaded = usePerksStore(selectorPerksLoaded);

  const { run: doFetchPlans, isLoading } = useAsync(fetchPerks, {
    fetchOnMount: !isPerksLoaded,
    setToStore: setPerks
  });

  return {
    isLoading: isLoading && !isPerksLoaded,
    loadPerks: doFetchPlans,
    perks
  };
}
