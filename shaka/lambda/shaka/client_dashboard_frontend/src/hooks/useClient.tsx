import { fetchClientData } from 'src/api/client';

import useStore, { createStore } from './useStore';
import { ClientData } from 'src/types/client';
import { useContext, useEffect } from 'react';
import { AuthContext } from 'src/contexts/AuthContext';

const clientID = import.meta.env.VITE_SHAKA_CLIENT_ID;
const fetchClient = () => fetchClientData(clientID);
const store = createStore<ClientData | null>(null);

export default function useClientData({
  fetchOnMount = false
}: {
  fetchOnMount?: boolean;
} = {}) {
  const { isLoggedIn } = useContext(AuthContext);
  const { state, fetch, isLoading, setState } = useStore({
    store,
    fetchOnMount: false,
    fetchFn: fetchClient
  });

  useEffect(() => {
    if (!isLoggedIn) return;

    if (!state && fetchOnMount) {
      fetch();
    }
  }, [fetch, isLoggedIn]);

  return {
    isLoading,
    loadClientData: fetch,
    clientData: state,
    setClientData: setState
  };
}
