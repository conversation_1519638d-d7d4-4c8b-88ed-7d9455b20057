import { fetchPlans } from 'src/api/plans';
import { useAsync } from 'src/api/useApi';
import { setPlans, selectorPlansLoaded, usePlansStore } from 'src/store/plan';

export default function usePlanList() {
  const isPlansLoaded = usePlansStore(selectorPlansLoaded);
  const { run: doFetchPlans, isLoading } = useAsync(fetchPlans, {
    setToStore: setPlans,
    fetchOnMount: !isPlansLoaded
  });

  return {
    isLoading: isLoading && !isPlansLoaded,
    loadPlans: doFetchPlans
  };
}
