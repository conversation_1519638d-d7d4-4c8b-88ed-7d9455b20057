import { User, UserResponse } from 'src/types/users';
import api from './index';

export const fetchUsers = (): Promise<UserResponse> =>
  api.get(`/frontend/dashboard-users/`).then((res) => res.data);

export const createUser = (payload: Partial<User>): Promise<UserResponse> =>
  api.post(`/frontend/dashboard-users/`, payload).then((res) => res.data);

export const updateUser = (id: string, payload: Partial<User>) =>
  api.put(`/frontend/dashboard-users/${id}/`, payload).then((res) => res.data);
