import { Subscriber, SubscriberInfo } from 'src/types/subscribers';
import api from './index';
import { toQueryString } from 'src/helper';

export const fetchSubscribers = (): Promise<Subscriber[]> =>
  api.get(`/frontend/subscribers`).then((res) => res.data);

type SubscribersPaged = {
  results: Subscriber[];
  count: number;
};

export const fetchSubscribersPaged = (payload: {
  page: number;
  pageSize: number;
  ordering?: string;
}): Promise<SubscribersPaged> => {
  const qs = toQueryString(payload);

  return api.get(`/frontend/subscribers-paged/${qs}`).then((res) => res.data);
};

export const fetchSubscriber = (id: string): Promise<SubscriberInfo> =>
  api.get(`/frontend/subscriber-detail/${id}/`).then((res) => res.data);

export const assignSimToSubscriber = (payload: any) =>
  api.post(`/frontend/sim-assignment/`, payload).then((res) => res.data);
