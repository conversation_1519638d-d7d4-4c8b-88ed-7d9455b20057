import { Plan, Plans } from 'src/types/plans';
import api from './index';

export const fetchPlans = (): Promise<Plans> =>
  api.get(`/plans`).then((res) => res.data);

export const createCustomPlan = (payload: Partial<Plan>): Promise<Plans> =>
  api.post(`/plans/custom/`, payload).then((res) => res.data);

export const createBundledPlan = (payload: Partial<Plan>): Promise<Plans> =>
  api.post(`/plans/bundled/`, payload).then((res) => res.data);

export const updateCustomPlan = (
  id: string,
  payload: Partial<Plan>
): Promise<Plans> =>
  api.put(`/plans/custom/${id}/`, payload).then((res) => res.data);

export const updateBundledPlan = (
  id: string,
  payload: Partial<Plan>
): Promise<Plans> =>
  api.put(`/plans/bundled/${id}/`, payload).then((res) => res.data);
