import { BoltOnOffering, BoltonsDetails } from 'src/types/boltons';
import api from './index';

type BoltonPayload = {
  name: string;
  cost: string;
  status: string;
  availability_date: string;
};

export const fetchBoltOns = (): Promise<BoltonsDetails> =>
  api.get('/boltons/').then((res) => res.data);

export const updateBolton = (id: string | number, payload: BoltonPayload) =>
  api.put(`/boltons/${id}/`, payload).then((res) => res.data);

export const createBolton = (payload: BoltonPayload) =>
  api.post('/boltons/', payload).then((res) => res.data);

export const getBoltOnsOfferings = (): Promise<BoltOnOffering[]> =>
  api.get('/bolt-on-offerings/').then((res) => res.data);
