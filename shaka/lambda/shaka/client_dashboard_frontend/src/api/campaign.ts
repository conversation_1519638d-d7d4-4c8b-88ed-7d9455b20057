import { Campaign } from 'src/types/campaign';
import api from './index';

export const fetchCampaigns = (): Promise<Campaign[]> =>
  api.get('/campaigns/').then((res) => res.data);

export const saveCampaign = (payload: Partial<Campaign>): Promise<Campaign> =>
  api.post('/campaigns/', payload).then((res) => res.data);

export const updateCampaign = (
  id: string | number,
  payload: Partial<Campaign>
): Promise<Campaign> =>
  api.put(`/campaigns/${id}/`, payload).then((res) => res.data);
