# Generated by Django 4.2.7 on 2024-02-13 22:35

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ProviderAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('fake-bar-data-transatel', 'Fake Bar Data (Transatel)'), ('fake-plan-upgrade-transatel', 'Fake Plan Upgrade (Transatel)')], max_length=100)),
                ('data_payload', models.JSONField(blank=True, null=True)),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('errored_retryable', 'Errored - Retryable'), ('errored_not_retryable', 'Errored - Not Retryable'), ('completed', 'Completed')], default='not_started', max_length=100)),
                ('last_updated', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'ordering': ['-id'],
            },
        ),
    ]
