# Generated by Django 4.2.7 on 2024-09-22 21:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('provider', '0004_alter_provideraction_action'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='provideraction',
            name='action',
            field=models.CharField(choices=[('fake-bar-data-transatel', 'Fake Bar Data (Transatel)'), ('fake-plan-upgrade-transatel', 'Fake Plan Upgrade (Transatel)'), ('real-bar-data-transatel', 'Real Bar Data (Transatel)'), ('real-plan-upgrade-transatel', 'Real Plan Upgrade (Transatel)'), ('real-activate-sim-transatel', 'Real Activate Sim (Transatel)'), ('real-request-port-in-transatel', 'Real Request Port In (Transatel)'), ('gamma-provision-service', 'Gamma Provision Service'), ('gamma-activate-service', 'Gamma Activate Service'), ('gamma-change-plan', 'Gamma Change Plan')], max_length=100),
        ),
    ]
