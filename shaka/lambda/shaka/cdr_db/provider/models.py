from django.db import models
from django.utils import timezone

class ProviderAction(models.Model):
    class ActionChoices(models.TextChoices):
        FAKE_BAR_DATA_TRANSATEL = 'fake-bar-data-transatel', 'Fake Bar Data (Transatel)'
        FAKE_PLAN_UPGRADE_TRANSATEL = 'fake-plan-upgrade-transatel', 'Fake Plan Upgrade (Transatel)'
        REAL_BAR_DATA_TRANSATEL = 'real-bar-data-transatel', 'Real Bar Data (Transatel)'
        REAL_PLAN_UPGRADE_TRANSATEL = 'real-plan-upgrade-transatel', 'Real Plan Upgrade (Transatel)'
        REAL_ACTIVATE_SIM_TRANSATEL = 'real-activate-sim-transatel', 'Real Activate Sim (Transatel)'
        REAL_REQUEST_PORT_IN_TRANSATEL = 'real-request-port-in-transatel', 'Real Request Port In (Transatel)'
        GAMMA_PROVISION_SERVICE = 'gamma-provision-service', 'Gamma Provision Service'
        GAMMA_ACTIVATE_SERVICE = 'gamma-activate-service', 'Gamma Activate Service'
        GAMMA_CHANGE_PLAN = 'gamma-change-plan', 'Gamma Change Plan'
        GAMMA_REQUEST_PORT_IN = 'gamma-request-port-in', 'Gamma Request Port In'
        GAMMA_ESTABLISH_CAPPING = 'gamma-establish-capping', 'Gamma Establish Capping'
        GAMMA_REAL_BAR_DATA = 'gamma-real-bar-data', 'Gamma Real Bar Data'
        GAMMA_REAL_UNBAR_DATA = 'gamma-real-unbar-data', 'Gamma Real Unbar Data'
        GAMMA_REAL_BAR_ROAMING = 'gamma-real-bar-roaming', 'Gamma Real Bar Roaming'
        GAMMA_LOOK_FOR_PAC_OUT ='gamma-look-for-pac-out', 'Gamma Look For Pac Out'
        GAMMA_CANCEL_SERVICE = 'gamma-cancel-service', 'Gamma Cancel Service'

    class StatusChoices(models.TextChoices):
        NOT_STARTED = 'not_started', 'Not Started'
        IN_PROGRESS = 'in_progress', 'In Progress'
        ERRORED_RETRYABLE = 'errored_retryable', 'Errored - Retryable'
        ERRORED_NOT_RETRYABLE = 'errored_not_retryable', 'Errored - Not Retryable'
        COMPLETED = 'completed', 'Completed'

    action = models.CharField(max_length=100, choices=ActionChoices.choices)
    data_payload = models.JSONField(blank=True, null=True)
    status = models.CharField(max_length=100, choices=StatusChoices.choices, default=StatusChoices.NOT_STARTED)
    last_updated = models.DateTimeField(default=timezone.now)
    start_after = models.DateTimeField(blank=True, null=True)

    performable_statuses = [StatusChoices.NOT_STARTED, StatusChoices.ERRORED_RETRYABLE]

    @property
    def is_performable(self):
        return self.status in self.performable_statuses

    def __str__(self):
        return f"{self.action} - {self.status}"

    @classmethod
    def get_all_performable(cls):
        is_startable_or_no_start_date = models.Q(start_after__lte=timezone.now()) | models.Q(start_after__isnull=True)
        return cls.objects.filter(status__in=cls.performable_statuses).filter(is_startable_or_no_start_date)

    class Meta:
        ordering = ['-id']


class Pollable(models.Model):
    class PollingTypeChoices(models.TextChoices):
        SERVICE_PROVISIONED = 'service_provisioned', 'Service Provisioned'
        SERVICE_ACTIVATED = 'service_activated', 'Service Activated'
        SERVICE_ON_PLAN = 'service_on_plan', 'Service On Plan'
        PORT = 'port', 'Port'
        SERVICE_BARRED = 'service_barred', 'Service Barred'
        SERVICE_UNBARRED = 'service_unbarred', 'Service Unbarred'
        SERVICE_CANCELLED = 'service_cancelled', 'Service Cancelled'

    class PollingStatusChoices(models.TextChoices):
        IN_PROGRESS = 'in_progress', 'In Progress'
        COMPLETED = 'completed', 'Completed'
        ERRORED = 'errored', 'Errored'
        TIMED_OUT = 'timed_out', 'Timed Out'

    polling_type = models.CharField(max_length=100, choices=PollingTypeChoices.choices)
    status = models.CharField(max_length=100, choices=PollingStatusChoices.choices, default=PollingStatusChoices.IN_PROGRESS)
    last_updated = models.DateTimeField(auto_now=True)
    poll_count = models.IntegerField(default=0)
    error_count = models.IntegerField(default=0)
    started_on = models.DateTimeField(default=timezone.now)
    params = models.JSONField(blank=True, null=True)

    @staticmethod
    def trigger_polling(polling_type, params=None):
        return Pollable.objects.create(polling_type=polling_type, params=params or {})

    @staticmethod
    def get_all_pollable():
        return Pollable.objects.filter(status=Pollable.PollingStatusChoices.IN_PROGRESS)
