from django.contrib import admin
from .models import ProviderAction, Pollable

class ProviderActionAdmin(admin.ModelAdmin):
    list_display = ['action', 'status', 'last_updated']
    list_filter = ['action', 'status']
    search_fields = ['action']
    readonly_fields = ['last_updated']
    fields = ['action', 'data_payload', 'status', 'last_updated']
    actions = ['mark_as_completed']

    def mark_as_completed(self, request, queryset):
        queryset.update(status=ProviderAction.StatusChoices.COMPLETED)
    mark_as_completed.short_description = "Mark selected actions as completed"

class PollableAdmin(admin.ModelAdmin):
    list_display = ['polling_type', 'status', 'started_on', 'last_updated', 'params']
    list_filter = ['polling_type', 'status']
    search_fields = ['polling_type', 'params']
    readonly_fields = ['last_updated', 'started_on']

admin.site.register(ProviderAction, ProviderActionAdmin)
admin.site.register(Pollable, PollableAdmin)
