import logging
from django.db import transaction
from core.slack import send_debug_slack_message
from .models import ProviderAction
from .clients import GammaClient, TransatelClient

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class Action:
    provider = None

    def __init__(self, action_instance):
        self.action_instance = action_instance

    def update_action_status(self, status):
        with transaction.atomic():
            self.action_instance.status = status
            self.action_instance.save()

    def try_progress_action(self):
        if self.action_instance.is_performable:
            with transaction.atomic():
                self.action_instance = ProviderAction.objects.select_for_update().get(pk=self.action_instance.pk)
                if self.action_instance.is_performable:
                    self.action_instance.status = ProviderAction.StatusChoices.IN_PROGRESS
                    self.action_instance.save()
                    return True
        return False

    def perform(self):
        logger.info('Considering action %s', self.action_instance)
        if self.try_progress_action():
            try:
                logger.info('Starting action %s', self.action_instance)
                self._perform(self.action_instance)
                self.update_action_status(ProviderAction.StatusChoices.COMPLETED)
            except Exception as e:  # pylint: disable=broad-exception-caught
                self.update_action_status(ProviderAction.StatusChoices.ERRORED_NOT_RETRYABLE)
                send_debug_slack_message(f'Error performing action {self.action_instance.id}: {e}')
                logger.exception("Error performing action for ProviderAction id %s: %s", self.action_instance.id, e)
        else:
            raise RuntimeError(f'Action not performable after select for update, might be overloaded?: {self.action_instance.pk}')

    def _perform(self, action_instance):
        raise NotImplementedError


class TransatelAction(Action):
    provider = 'transatel'

    def _perform(self, action_instance):
        raise NotImplementedError


class FakeTransatelAction(TransatelAction):
    def _perform(self, action_instance):
        logger.info('Faking transatel action %s : %s', action_instance.action, action_instance.data_payload)

class RealTransatelAction(TransatelAction):
    def _perform(self, action_instance):
        raise NotImplementedError

class RealBarDataAction(RealTransatelAction):
    def _perform(self, action_instance):
        response = TransatelClient().bar_data(action_instance.data_payload['sim_serial'])
        logger.info('Bar response %s', response)

class RealPlanUpgradeAction(RealTransatelAction):
    def _perform(self, action_instance):
        if action_instance.data_payload['transatel_package_id'] not in ['TSL_UK_DATA_100GB', 'TSL_UK_DATA_30GB', 'TSL_UK_DATA_10GB']:
            raise RuntimeError('Unknown target package')
        if action_instance.data_payload['transatel_package_id'] == 'TSL_UK_DATA_100GB':
            response = TransatelClient().upgrade_to_100gb(action_instance.data_payload['sim_serial'])
        elif action_instance.data_payload['transatel_package_id'] == 'TSL_UK_DATA_30GB':
            response = TransatelClient().upgrade_to_30gb(action_instance.data_payload['sim_serial'])
        else:
            response = TransatelClient().upgrade_to_10gb(action_instance.data_payload['sim_serial'])
        logger.info('Upgrade response %s', response)

class RealActivateSimAction(RealTransatelAction):
    def _perform(self, action_instance):
        if action_instance.data_payload['transatel_package_id'] not in ['TSL_UK_DATA_100GB', 'TSL_UK_DATA_30GB', 'TSL_UK_DATA_10GB']:
            raise RuntimeError('Unknown target package')
        response = TransatelClient().activate_sim(action_instance.data_payload['sim_serial'], action_instance.data_payload['transatel_package_id'], action_instance.data_payload.get('reference', ''))
        logger.info('Activate response %s', response)


class RealRequestPortInAction(RealTransatelAction):
    def _perform(self, action_instance):
        response = TransatelClient().request_port_in(action_instance.data_payload['sim_serial'], action_instance.data_payload['pac_code'], action_instance.data_payload['msisdn'], action_instance.data_payload.get('desired_date', None))
        logger.info('Port in response %s', response)


class GammaAction(Action):
    provider = 'gamma'

    def _perform(self, action_instance):
        raise NotImplementedError

class GammaProvisionServiceAction(GammaAction):
    def _perform(self, action_instance):
        if action_instance.data_payload['gamma_package_id'] not in GammaClient.package_ids:
            raise RuntimeError('Unknown target package')
        response = GammaClient().provision_service(action_instance.data_payload['sim_serial'], action_instance.data_payload['gamma_package_id'], action_instance.data_payload['callback_url'])
        logger.info('Provision response %s', response)


class GammaActivateServiceAction(GammaAction):
    def _perform(self, action_instance):
        response = GammaClient().activate_service(action_instance.data_payload['msisdn'], action_instance.data_payload['callback_url'])
        logger.info('Activate service response %s', response)


class GammaChangePlanAction(GammaAction):
    def _perform(self, action_instance):
        if action_instance.data_payload['gamma_package_id'] not in GammaClient.package_ids:
            raise RuntimeError('Unknown target package')
        if action_instance.data_payload.get('fake'):
            logger.info('Faking plan change %s', action_instance.data_payload)
        else:
            response = GammaClient().change_plan(action_instance.data_payload['sim_serial'], action_instance.data_payload['gamma_package_id'], action_instance.data_payload['callback_url'])
            logger.info('Change plan response %s', response)


class GammaRequestPortInAction(GammaAction):
    def _perform(self, action_instance):
        sim_serial = action_instance.data_payload['sim_serial']
        pac_code = action_instance.data_payload['pac_code']
        new_msisdn = action_instance.data_payload['msisdn']
        port_date = action_instance.data_payload.get('desired_date', None)
        callback_url = action_instance.data_payload.get('callback_url')
        response = GammaClient().request_port_in(sim_serial, pac_code, new_msisdn, port_date, callback_url)
        return response


class GammaRealBarDataAction(GammaAction):
    def _perform(self, action_instance):
        sim_serial = action_instance.data_payload['sim_serial']
        callback_url = action_instance.data_payload.get('callback_url')
        response = GammaClient().bar_data(sim_serial, callback_url)
        return response

class GammaRealUnbarDataAction(GammaAction):
    def _perform(self, action_instance):
        sim_serial = action_instance.data_payload['sim_serial']
        callback_url = action_instance.data_payload.get('callback_url')
        response = GammaClient().unbar_data(sim_serial, callback_url)
        return response

class GammaRealBarRoamingAction(GammaAction):
    def _perform(self, action_instance):
        msisdn = action_instance.data_payload['msisdn']
        response = GammaClient().bar_roaming(msisdn)
        return response

class GammaEstablishCappingAction(GammaAction):
    def _perform(self, action_instance):
        response = GammaClient().establish_capping(action_instance.data_payload['msisdn'])
        logger.info('Bar response %s', response)

class GammaLookForPacOutAction(GammaAction):
    def _perform(self, action_instance):
        c = GammaClient()
        sim_serials = action_instance.data_payload['sim_serials']
        for sim_serial in sim_serials:
            try:
                service = c.get_service_by_sim(sim_serial)
                if service.get('status', '') in ['CEASED', 'SUSPENDED']:
                    logger.info('Service %s is %s', sim_serial, service['status'])
                    send_debug_slack_message(f'Service {sim_serial} is {service["status"]}')
                elif service.get('status', '') != 'ACTIVE':
                    logger.info('Service strange status %s for %s', service['status'], sim_serial)
                    send_debug_slack_message(f'Service strange status {service["status"]} for {sim_serial}')
            except IndexError:
                # Probably terminated
                try:
                    sim = c.get_sim_by_sim_serial(sim_serial)
                    if sim['status'] in ['CEASED', 'SUSPENDED']:
                        logger.info('Service sim %s is %s', sim_serial, sim['status'])
                        send_debug_slack_message(f'Service sim {sim_serial} is {sim["status"]}')
                    elif sim['status'] != 'IN_USE':
                        logger.info('Sim strange status %s for %s', sim['status'], sim_serial)
                        send_debug_slack_message(f'Sim strange status {sim["status"]} for {sim_serial}')
                except IndexError:
                    send_debug_slack_message(f'Sim {sim_serial} not found so probably terminated')

class GammaCancelServiceAction(GammaAction):
    def _perform(self, action_instance):
        response = GammaClient().cancel_service(action_instance.data_payload['sim_serial'], action_instance.data_payload['callback_url'])
        logger.info('Cancel response %s', response)

action_classes = {
    ProviderAction.ActionChoices.FAKE_BAR_DATA_TRANSATEL: FakeTransatelAction,
    ProviderAction.ActionChoices.FAKE_PLAN_UPGRADE_TRANSATEL: FakeTransatelAction,
    ProviderAction.ActionChoices.REAL_BAR_DATA_TRANSATEL: RealBarDataAction,
    ProviderAction.ActionChoices.REAL_PLAN_UPGRADE_TRANSATEL: RealPlanUpgradeAction,
    ProviderAction.ActionChoices.REAL_ACTIVATE_SIM_TRANSATEL: RealActivateSimAction,
    ProviderAction.ActionChoices.REAL_REQUEST_PORT_IN_TRANSATEL: RealRequestPortInAction,
    ProviderAction.ActionChoices.GAMMA_PROVISION_SERVICE: GammaProvisionServiceAction,
    ProviderAction.ActionChoices.GAMMA_ACTIVATE_SERVICE: GammaActivateServiceAction,
    ProviderAction.ActionChoices.GAMMA_CHANGE_PLAN: GammaChangePlanAction,
    ProviderAction.ActionChoices.GAMMA_REQUEST_PORT_IN: GammaRequestPortInAction,
    ProviderAction.ActionChoices.GAMMA_ESTABLISH_CAPPING: GammaEstablishCappingAction,
    ProviderAction.ActionChoices.GAMMA_REAL_BAR_DATA: GammaRealBarDataAction,
    ProviderAction.ActionChoices.GAMMA_REAL_UNBAR_DATA: GammaRealUnbarDataAction,
    ProviderAction.ActionChoices.GAMMA_REAL_BAR_ROAMING: GammaRealBarRoamingAction,
    ProviderAction.ActionChoices.GAMMA_LOOK_FOR_PAC_OUT: GammaLookForPacOutAction,
    ProviderAction.ActionChoices.GAMMA_CANCEL_SERVICE: GammaCancelServiceAction,
}


def perform_action(action_instance):
    action_classes[action_instance.action](action_instance).perform()

def perform_all_actions():
    all_actions_succeeded = True

    try:
        actions = ProviderAction.get_all_performable()

        for action_instance in actions:
            try:
                perform_action(action_instance)
            except Exception as e:  # pylint: disable=broad-exception-caught
                logger.exception("Error processing action for ProviderAction id %s: %s", action_instance.id, e)
                all_actions_succeeded = False

    except Exception as e:  # pylint: disable=broad-exception-caught
        logger.exception("Error processing all actions: %s", e)
        all_actions_succeeded = False
    return all_actions_succeeded
