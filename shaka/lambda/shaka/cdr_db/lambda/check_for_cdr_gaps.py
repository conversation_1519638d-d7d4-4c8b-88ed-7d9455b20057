# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import logging
from collections import defaultdict
from datetime import datetime, timedelta

import django
import pytz

django.setup()

from core.models import FileImport

logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(_, __):
    logger.info('Starting')
    if FileImport.objects.filter(status=FileImport.Status.ERRORED).exists():
        raise RuntimeError('Errored file imports exist')
    logger.info('Nothing errored')
    lon = pytz.timezone('Europe/London')
    start_date = lon.localize(datetime.now() - timedelta(days=31))
    end_date = lon.localize(datetime.now() - timedelta(hours=1))
    seqs = defaultdict(set)
    for file_import in FileImport.objects.all():
        if file_import.datestamp >= start_date and file_import.datestamp < end_date:
            seqs[file_import.data_type].add(file_import.sequence_number)
    for data_type, sequence_ids in seqs.items():
        if sequence_ids and len(sequence_ids) > 1:
            if max(sequence_ids) - min(sequence_ids) != len(sequence_ids) - 1:
                raise RuntimeError(f'Missing sequence id for {data_type}')
            else:
                logger.info('Type %s has %s records with %s max %s min %s delta', data_type, len(sequence_ids), max(sequence_ids), min(sequence_ids), max(sequence_ids) - min(sequence_ids))
