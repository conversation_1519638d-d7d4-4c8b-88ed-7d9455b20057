import logging
import os
import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(_, __):
    api_endpoint = os.environ['CDR_DB_ENDPOINT']
    api_token = os.environ['CDR_DB_API_TOKEN']

    headers = {
        'Authorization': f'Bearer {api_token}',
        'Content-Type': 'application/x-www-form-urlencoded',
    }

    logger.info('Posting to %s', api_endpoint)
    response = requests.post(api_endpoint, data={}, headers=headers, timeout=60)
    response.raise_for_status()
