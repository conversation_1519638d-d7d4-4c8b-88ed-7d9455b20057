import csv
import gzip
import hashlib
from datetime import datetime
from io import StringIO

import pytz

from core.models import MVNO, SMSCDR, DataCDR, VoiceCDR

MVNO_CACHE = None

def get_or_create_mvno(mvno):
    global MVNO_CACHE
    if MVNO_CACHE is None:
        MVNO_CACHE = {mvno.id: mvno for mvno in MVNO.objects.all()}
    if mvno not in MVNO_CACHE:
        obj = MVNO.objects.create(id=mvno)
        MVNO_CACHE[mvno] = obj
    return MVNO_CACHE[mvno]


def parse_timezone_dt(value):
    timezone = 'Europe/London'
    return pytz.timezone(timezone).localize(
        datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
    )

class CDRImporter:
    model_class = None

    def __init__(self, file_on_disk):
        self.file_on_disk = file_on_disk
        self.header_definitions = self.build_header_definitions()
        self.csv_parser = None
        self.io_channel = None
        self.filename = file_on_disk.name

    def build_header_definitions(self):
        definitions = {}
        for header in self.get_headers():
            if isinstance(header, tuple):
                header_name, fn = header
            else:
                header_name = header
                fn = lambda x: x
            definitions[header_name] = fn
        return definitions

    def get_headers(self):
        raise NotImplementedError()

    def build_csv_parser(self):
        self.io_channel = StringIO()
        reader = csv.DictReader(self.io_channel, fieldnames=list(self.header_definitions.keys()), delimiter=';')
        return reader

    def calculate_checksum(self, objects):
        md5 = hashlib.md5()
        md5.update(str(sum(o.record_id for o in objects)).encode('iso-8859-1'))
        return md5.hexdigest()

    def is_checksum_line(self, line):
        return len(line) < 40

    def convert_to_unsaved_objects(self):
        objects = []
        open_fn = gzip.open if self.filename.endswith('gz') else open
        with open_fn(self.file_on_disk, mode='rt', encoding='iso-8859-1') as f:
            checksum_line = None
            for line in f:
                if self.is_checksum_line(line):
                    checksum_line = line
                else:
                    if checksum_line is None:
                        objects.append(self.prepare_unsaved_object(line))
                    else:
                        raise RuntimeError('Checksum line appeared too early in the file?')
        count, expected_md5_hash = checksum_line.strip().split(';')
        if int(count) != len(objects):
            raise RuntimeError('Incorrect count')
        actual_md5_hash = self.calculate_checksum(objects)
        if expected_md5_hash != actual_md5_hash:
            raise RuntimeError('Incorrect hash')
        return objects

    def parse_line(self, line):
        if self.csv_parser is None:
            self.csv_parser = self.build_csv_parser()
        bytes_written = self.io_channel.write(line + '\n')
        self.io_channel.seek(self.io_channel.tell() - bytes_written)
        d = next(self.csv_parser)
        return {k: self.header_definitions[k](v) for k, v in d.items()}

    def prepare_unsaved_object(self, line):
        parsed_dict = self.parse_line(line)
        return self.get_model_class()(filename=self.filename, **parsed_dict)  # pylint: disable=not-callable

    def get_model_class(self):
        if self.model_class:
            return self.model_class
        raise RuntimeError('model_class not specified in an importer')


class VoiceImporter(CDRImporter):
    model_class = VoiceCDR

    def get_headers(self):
        return [
            ('record_id', int),
            'msisdn',
            'imsi',
            'subscriber_id',
            'cos',
            ('mvno', get_or_create_mvno),
            'call_type',
            'bearer',
            'anumber',
            'bnumber',
            'rnumber',
            'prefix',
            'destination',
            'location_zone',
            'location_code',
            'country_code',
            'mcc',
            'mnc',
            'cell_id',
            ('setup_time', parse_timezone_dt),
            ('answer_time', parse_timezone_dt),
            ('disconnect_time', parse_timezone_dt),
            ('duration', int)
        ]


class DataImporter(CDRImporter):
    model_class = DataCDR

    def get_headers(self):
        return [
            ('record_id', int),
            'msisdn',
            'imsi',
            'subscriber_id',
            'cos',
            ('mvno', get_or_create_mvno),
            'call_type',
            'apn',
            'location_zone',
            'location_code',
            'country_code',
            'mcc',
            'mnc',
            'cell_id',
            'rat',
            'imei',
            ('event_time', parse_timezone_dt),
            ('usu', int)
        ]


class SMSImporter(CDRImporter):
    model_class = SMSCDR

    def get_headers(self):
        return [
            ('record_id', int),
            'msisdn',
            'imsi',
            'subscriber_id',
            'cos',
            ('mvno', get_or_create_mvno),
            'call_type',
            'anumber',
            'bnumber',
            'prefix',
            'destination',
            'location_zone',
            'location_code',
            'country_code',
            'mcc',
            'mnc',
            ('event_time', parse_timezone_dt)
        ]


IMPORTERS = {
    'voice': VoiceImporter,
    'data': DataImporter,
    'sms': SMSImporter,
    # 'sms': SMSImporter
}
