# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import json
import logging
import email
import csv
from io import String<PERSON>
from email import policy
import os
import tempfile
from pathlib import Path

import boto3
import botocore.config
import django

django.setup()

# Now this script or any imported module can use any part of Django it needs.
from provider.models import ProviderAction
from core.slack import send_debug_slack_message

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class IgnorableFileError(RuntimeError):
    pass


def key_to_filename(s3_key):
    return s3_key.split('/')[-1]

def lambda_handler(sqs_event, __):
    bucket_name = os.environ['S3_BUCKET_NAME']
    s3 = boto3.resource('s3', config=botocore.config.Config(s3={'addressing_style':'path'}))
    bucket = s3.Bucket(bucket_name)

    with tempfile.TemporaryDirectory() as tempdir:
        for record in sqs_event['Records']:
            event_records = json.loads(record['body'])
            for event in event_records['Records']:
                if event['s3']['bucket']['name'] != bucket_name:
                    raise RuntimeError('Invalid bucket', event)
                key = event['s3']['object']['key']
                process_key(key, bucket, tempdir)

def process_key(key, bucket, tempdir):
    def download_s3_file(key):
        txt_filename = key_to_filename(key)
        txt_fpath = Path(tempdir) / txt_filename
        bucket.download_file(key, txt_fpath)
        return txt_fpath

    def process_alert_file(key):  # pylint: disable=too-many-locals
        logger.info('Downloading gamma alert file key %s', key)
        file_on_disk = download_s3_file(key)
        print(file_on_disk)
        with open(file_on_disk, 'r', encoding='utf-8') as f:
            text = f.read()
            logger.info('File contents: %s', text)
            print(text)
            raw_email = text
        msg = email.message_from_string(raw_email, policy=policy.default)
        attachments = []
        for part in msg.iter_parts():
            if part.get_content_disposition() == 'attachment':
                attachment_data = part.get_payload(decode=True)
                filename = part.get_filename()
                attachments.append((filename, attachment_data))
        logger.info('Attachments: %s', attachments)
        for filename, attachment_data in attachments:
            csv_bytestring = attachment_data
            csv_string = csv_bytestring.decode('latin1')
            csv_file = StringIO(csv_string)
            csv_reader = csv.DictReader(csv_file)
            column_values = []
            for row in csv_reader:
                for header_key, value in row.items():
                    if header_key.strip() == 'A Number':
                        column_values.append(value)

            print(column_values)
            logger.info('Column values: %s', column_values)
            for number_to_disable_roaming in column_values:
                print(number_to_disable_roaming)
                logger.info('Number to disable roaming: %s', number_to_disable_roaming)
                msisdn = phone_number_to_msisdn(number_to_disable_roaming)
                logger.info('MSISDN: %s', msisdn)
                ProviderAction.objects.create(action=ProviderAction.ActionChoices.GAMMA_REAL_BAR_ROAMING, data_payload={'msisdn': msisdn, 'source': key})
    filename = key_to_filename(key)
    process_alert_file(key)
    send_debug_slack_message(f'Processed Gamma alert file {filename}')
    return filename


def phone_number_to_msisdn(phone_number):
    stripped = phone_number.replace(' ', '')
    if stripped[0] == '0':
        return f'44{stripped[1:]}'
    else:
        return stripped.replace('+44', '44')
