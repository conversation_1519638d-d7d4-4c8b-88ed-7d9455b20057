from datetime import datetime, timed<PERSON>ta
from unittest import mock

import pytz
import time_machine
from django.conf import settings
from django.test import TestCase

from core.agnostic_cdr import topics as agnostic_topics
from core.models import FileImport
from core.provider_specific_cdr import events as provider_specific_cdr_events
from core.provider_specific_cdr import topics as provider_specific_cdr_topics

from .cdr_csv_importer import IMPORTERS
from .check_for_cdr_gaps import lambda_handler as check_for_cdr_gaps
from .import_cdr_file import is_strange_file


# Create your tests here.
class CDRImportTestCase(TestCase):
    def test_voice_cdr(self):
        objs = IMPORTERS['voice'](settings.BASE_DIR / 'lambda' / 'testdata' / 'sample_voice_cdr.csv').convert_to_unsaved_objects()
        self.assertEqual(1, len(objs))
        obj = objs[0]
        self.assertEqual('447421323213', obj.anumber)
        self.assertEqual(138993858136, obj.record_id)

    def test_data_cdr(self):
        objs = IMPORTERS['data'](settings.BASE_DIR / 'lambda' / 'testdata' / 'sample_data_cdr.csv').convert_to_unsaved_objects()
        self.assertEqual(4, len(objs))
        obj = objs[0]
        self.assertEqual('447712759144', obj.msisdn)
        self.assertEqual(138977228116, obj.record_id)

    def test_sms_cdr(self):
        objs = IMPORTERS['sms'](settings.BASE_DIR / 'lambda' / 'testdata' / 'sample_sms_cdr.csv').convert_to_unsaved_objects()
        self.assertEqual(1, len(objs))
        obj = objs[0]
        self.assertEqual('447721996428', obj.msisdn)
        self.assertEqual(139037792050, obj.record_id)

    def test_strange_file(self):
        self.assertTrue(is_strange_file('130450534664;447421122769;234336570058057;26187164;17208693;01261673.00000012;ACTIVE;MVNA_UK_EEL_SHAKA;UK_REF_COS;UK_REF_RCOS_202110;MOG;UK_REF_Z_HPLMN;GBR;149.254.0.250;UK_REF_PAYM_RG_DATA_EVERYWHERE;EVERYWHERE;2023-08-08 18:11:41;scc02wvnmmk.epc.mnc030.mcc234.3gppnetwork.org,1691501327,490301730,64d2430f-234336570058057;10.195.4.104;;33556731;0;0;;;;;0;0;0;;;;0;0;0;;;;0;0;0;;;;0;0;0;;;;0;0;0'))
        self.assertFalse(is_strange_file('138977228116;447719759144;234336570058491;01261673.00000015;UK_REF_COS;MVNA_UK_EEL_SHAKA;MOG;everywhere;UK_REF_Z_HPLMN;*************;GBR;234;30;8232F4032A3132F40300920C01;06;534915819687256;2023-11-29 18:24:14;8226'))


class TestGapCheck(TestCase):
    def test_does_not_raise_if_no_imports(self):
        check_for_cdr_gaps(None, None)  # Does not raise

    def test_does_not_raise_if_no_errored_imports(self):
        self.create_data_import(102)
        self.create_voice_import(10, status=FileImport.Status.SKIPPED_STRANGE)
        self.create_sms_import(3, status=FileImport.Status.IN_PROGRESS)
        check_for_cdr_gaps(None, None)  # Does not raise

    def test_does_not_raise_if_no_sequence_gap(self):
        self.create_voice_import(9)
        self.create_voice_import(10)
        self.create_voice_import(11)
        check_for_cdr_gaps(None, None)  # Does not raise

    def test_raises_if_any_errored(self):
        self.create_voice_import(10, status=FileImport.Status.ERRORED)
        with self.assertRaises(RuntimeError):
            check_for_cdr_gaps(None, None)

    def test_raises_if_sequence_gap(self):
        self.create_data_import(28)
        self.create_data_import(29)
        self.create_data_import(31)
        self.create_data_import(32)
        with self.assertRaises(RuntimeError):
            check_for_cdr_gaps(None, None)

    def create_import(self, data_type, sequence_number, status):
        lon = pytz.timezone('Europe/London')
        start_date = lon.localize(datetime.now() - timedelta(days=10))
        datestring = start_date.strftime('%Y%m%d%H%M%S')
        return FileImport.objects.create(filename=f'cdr_{data_type}_MVNA_UK_EEL_SHAKA_{sequence_number}_{datestring}.csv.gz', status=status)

    def create_data_import(self, sequence_number, status=FileImport.Status.DONE):
        return self.create_import('data', sequence_number, status)

    def create_voice_import(self, sequence_number, status=FileImport.Status.DONE):
        return self.create_import('voice', sequence_number, status)

    def create_sms_import(self, sequence_number, status=FileImport.Status.DONE):
        return self.create_import('sms', sequence_number, status)


class TestProviderSpecificCDRReceived:
    nullable_gamma_field_we_dont_care_about = [
        "call_type", "call_cause_definition_required", "customer_identifier", "non_charged_party", "call_date",
        "call_time", "duration", "bytes_transmitted", "bytes_received", "description", "chargecode", "time_band",
        "salesprice", "salesprice_pre_bundle", "extension", "ddi", "grouping_id", "call_class_feature", "carrier",
        "recording", "vat", "country_of_origin", "network", "retail_tariff_code", "remote_network", "apn",
        "diverted_number", "ring_time", "record_id", "currency", "caller_line_identity", "network_access_reference",
        "ngcs_access_charge", "ngcs_service_charge", "total_bytes_transferred", "user_id", "onward_billing_reference",
        "contract_name", "bundle_name", "bundle_allowance", "discount_reference", "routing_code",
    ]

    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_data_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "data_usage_bytes": 1024,
            "roaming_zone": "A",
            "__type": provider_specific_cdr_events.GammaDataCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in self.nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime(2025, 1, 1)):
            provider_specific_cdr_topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'data_usage_bytes': 1024,
                'roaming_zone': 'A',
                '__type': 'AgnosticDataCDREvent',
                '__published_at': **********.0
            }
        )

    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_voice_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "duration_seconds": 60,
            "__type": provider_specific_cdr_events.GammaVoiceCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in self.nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime(2025, 1, 1)):
            provider_specific_cdr_topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'duration_seconds': 60,
                '__type': 'AgnosticVoiceCDREvent',
                '__published_at': **********.0
            }
        )

    @mock.patch.object(agnostic_topics.agnostic_cdr_generated.publisher, "publish")
    def test_can_handle_gamma_sms_event(self, mock_publish):
        test_event = {
            "filename": "some_filename",
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "quantity": 1,
            "__type": provider_specific_cdr_events.GammaSMSCDREvent.__name__,
            "__published_at": **********.0,
            **{field: None for field in self.nullable_gamma_field_we_dont_care_about}
        }

        with time_machine.travel(datetime(2025, 1, 1)):
            provider_specific_cdr_topics.provider_specific_cdr_received.handle(test_event)

        mock_publish.assert_called_once_with(
            {
                'msisdn': '1234',
                'event_time': '2025-01-01T00:00:00',
                'quantity': 1,
                '__type': 'AgnosticSMSCDREvent',
                '__published_at': **********.0
            }
        )
