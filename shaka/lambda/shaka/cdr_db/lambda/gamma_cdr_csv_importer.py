from collections import defaultdict
import csv
from decimal import Decimal
from datetime import datetime

import pytz

from core.models import GammaSMSCDR, GammaDataCDR, GammaVoiceCDR
from core.provider_specific_cdr import from_models, topics
from .gamma_countries import two_letter_by_three_letter, zone_by_two_letter

def int_or_none(value):
    return int(value) if value or value == 0 else None

def decimal_or_none(value):
    return Decimal(value) if value is not None else None

# def parse_timezone_dt(value):
#     timezone = 'Europe/London'
#     return pytz.timezone(timezone).localize(
#         datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
#     )

def calculate_roaming_zone(country_code):
    if country_code:
        two_letter = two_letter_by_three_letter.get(country_code)
        if two_letter:
            zone = zone_by_two_letter.get(two_letter)
            if zone:
                return GammaDataCDR.RoamingZones.get_by_letter(zone)
            else:
                raise ValueError(f'Unknown zone for country code {country_code}')
        else:
            raise ValueError(f'Unknown country code {country_code}')
    return GammaDataCDR.RoamingZones.A

class GammaCDRImporter:
    def __init__(self, file_on_disk):
        self.file_on_disk = file_on_disk
        self.header_definitions = self.build_header_definitions()
        self.csv_parser = None
        self.filename = file_on_disk.name

    def build_header_definitions(self):
        definitions = {}
        for header in self.get_headers():
            if isinstance(header, tuple):
                header_name, fn = header
            else:
                header_name = header
                fn = lambda x: x
            definitions[header_name] = fn
        return definitions

    def _header_name_to_field_name(self, header_name):
        return header_name.lower().replace(' ', '_').replace('(', '').replace(')', '').replace('-', '_')

    def _prepare_extra_data(self, model, prepared_data):
        extra_data = {}
        if model == GammaDataCDR:
            extra_data['data_usage_bytes'] = prepared_data['total_bytes_transferred']
            extra_data['roaming_zone'] = calculate_roaming_zone(prepared_data['country_of_origin'])
        elif model == GammaVoiceCDR:
            extra_data['duration_seconds'] = prepared_data['duration']
        elif model == GammaSMSCDR:
            extra_data['quantity'] = 1

        extra_data['event_time'] = pytz.timezone('Europe/London').localize(
            datetime.strptime(f"{prepared_data['call_date']} {prepared_data['call_time']}", "%d/%m/%Y %H:%M:%S")
        )
        extra_data['msisdn'] = prepared_data['customer_identifier'].replace('+', '')
        return extra_data

    def save_to_db(self):
        objects = defaultdict(list)
        with open(self.file_on_disk, mode='r', encoding='utf-8') as f:
            reader = csv.DictReader(f, fieldnames=list(self.header_definitions.keys()))
            for line in reader:
                if line['Call Type'] == 'Call Type':
                    continue
                cdr_data = {k: self.header_definitions[k](v) for k, v in line.items()}
                if cdr_data['Call Type'] == 'MD' or cdr_data['Call Type'] == 'MD-T':
                    model = GammaDataCDR
                elif cdr_data['Call Type'] == 'M' or cdr_data['Call Type'] == 'M-T':
                    model = GammaVoiceCDR
                elif cdr_data['Call Type'] == 'S':
                    model = GammaSMSCDR
                else:
                    raise ValueError(f'Unknown call type {cdr_data["Call Type"]}, {cdr_data}')
                prepared_data = {self._header_name_to_field_name(k): v for k, v in cdr_data.items()}
                with_extra_fields = {**prepared_data, **self._prepare_extra_data(model, prepared_data)}
                objects[model].append(model(**with_extra_fields, filename=self.filename))
        for model, instances in objects.items():
            model.objects.bulk_create(instances)

    def get_headers(self):
        return [
            "Call Type",
            "Call Cause Definition Required",
            "Customer Identifier",
            "Non-Charged Party",
            "Call Date",
            "Call Time",
            ("Duration", int_or_none),
            ("Bytes Transmitted", int_or_none),
            ("Bytes Received", int_or_none),
            "Description",
            "Chargecode",
            "Time Band",
            ("Salesprice", decimal_or_none),
            "Salesprice (Pre-Bundle)",
            "Extension",
            "DDI",
            "Grouping ID",
            "Call Class (Feature)",
            "Carrier",
            "Recording",
            "VAT",
            "Country of Origin",
            "Network",
            "Retail Tariff Code",
            "Remote Network",
            "APN",
            "Diverted Number",
            "Ring time",
            "Record ID",
            "Currency",
            "Caller Line Identity",
            "Network Access Reference",
            "NGCS Access Charge",
            "NGCS Service Charge",
            ("Total Bytes Transferred", int_or_none),
            "User ID",
            "Onward Billing Reference",
            "Contract Name",
            "Bundle Name",
            ("Bundle Allowance", int_or_none),
            "Discount Reference",
            "Routing Code"
        ]

    def _publish_event_for_cdr_instance(self, cdr_instance: GammaDataCDR | GammaVoiceCDR | GammaSMSCDR) -> None:
        event = {
            GammaDataCDR: from_models.gamma_data_model_instance_to_event,
            GammaVoiceCDR: from_models.gamma_voice_model_instance_to_event,
            GammaSMSCDR: from_models.gamma_sms_model_instance_to_event,
        }[type(cdr_instance)](cdr_instance)
        topics.provider_specific_cdr_received.publish(event)
