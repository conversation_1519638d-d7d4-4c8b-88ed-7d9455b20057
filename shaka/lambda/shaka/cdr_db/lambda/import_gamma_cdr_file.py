# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import json
import logging
import os
import tempfile
from pathlib import Path

import boto3
import botocore.config
import django

django.setup()

# Now this script or any imported module can use any part of Django it needs.
from django.db import transaction

from core.models import GammaFileImport, FileImportStatus

from .gamma_cdr_csv_importer import GammaCDRImporter

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class IgnorableFileError(RuntimeError):
    pass


def key_to_cdr_filename(s3_key):
    return s3_key.split('/')[-1]

def filename_to_cdr_type(filename):
    return filename.split('_')[1]

def insert_cdr_objects(file_on_disk):
    importer = GammaCDRImporter(file_on_disk)
    importer.save_to_db()

def get_file_parts(filename):
    without_extension = filename.split('.')[0]
    _, freq, data_type, acc, date_portion, _, _, extra, _ = without_extension.split('_')
    return {
        'freq': freq,
        'data_type': data_type,
        'acc': acc,
        'date_portion': date_portion,
        'extra': extra,
    }


def check_for_ignorable_file(file_on_disk):
    filename = file_on_disk.name
    if get_file_parts(filename)['extra'] in ['MOB', 'MOBD']:
        raise IgnorableFileError()


def lambda_handler(sqs_event, __):
    bucket_name = os.environ['S3_BUCKET_NAME']
    s3 = boto3.resource('s3', config=botocore.config.Config(s3={'addressing_style':'path'}))
    bucket = s3.Bucket(bucket_name)
    files_completed = []

    with tempfile.TemporaryDirectory() as tempdir:
        for record in sqs_event['Records']:
            event_records = json.loads(record['body'])
            for event in event_records['Records']:
                if event['s3']['bucket']['name'] != bucket_name:
                    raise RuntimeError('Invalid bucket', event)
                key = event['s3']['object']['key']
                potentially_completed_file = process_key(key, bucket, tempdir)
                if potentially_completed_file:
                    files_completed.append(potentially_completed_file)
    if files_completed:
        logger.info('Imported %s', files_completed)
    else:
        logger.info('No files to import')


def process_key(key, bucket, tempdir):
    def download_s3_file(key):
        txt_filename = key_to_cdr_filename(key)
        txt_fpath = Path(tempdir) / txt_filename
        bucket.download_file(key, txt_fpath)
        return txt_fpath

    def process_cdr_file(key):
        logger.info('Downloading gamma file %s', key)
        file_on_disk = download_s3_file(key)
        check_for_ignorable_file(file_on_disk)
        insert_cdr_objects(file_on_disk)

    filename = key_to_cdr_filename(key)
    with transaction.atomic():
        file_import = GammaFileImport.objects.select_for_update().filter(filename=filename).first()
        if file_import is None:
            file_import = GammaFileImport.objects.create(filename=filename, status=FileImportStatus.IN_PROGRESS)
            logger.info('Importing cdr file %s', key)
        elif file_import.status == FileImportStatus.SKIPPED_IGNORABLE:
            logger.info('This file is detected as skipped (ignorable) %s', key)
            return None
        elif file_import.status == FileImportStatus.IN_PROGRESS:
            logger.info('Another lambda is currently processing %s', key)
            return None
        elif file_import.status == FileImportStatus.ERRORED:
            logger.info('Trying %s again because it errored previously', key)
        else:
            logger.info('Another lambda has finished processing %s with status %s', key, file_import.status)
            return None
    try:
        process_cdr_file(key)
        file_import.status = FileImportStatus.DONE
        file_import.save()
        logger.info('Completed %s', key)
        return filename
    except IgnorableFileError:
        file_import.status = FileImportStatus.SKIPPED_IGNORABLE
        file_import.save()
        logger.warning('Found ignorable file %s', key, exc_info=True)
        return None
    except:
        file_import.status = FileImportStatus.ERRORED
        file_import.save()
        logger.error('Errored %s', key, exc_info=True)
        raise
    return None
