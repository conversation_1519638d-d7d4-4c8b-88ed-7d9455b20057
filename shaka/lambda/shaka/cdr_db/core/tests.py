from datetime import datetime

import pytz
from dateutil.relativedelta import relativedelta
from django.contrib.auth import get_user_model
from django.test import TestCase

from core.models import FileImport

from .test_utils import (DEFAULT_MSISDN, create_data_record, create_sms_record,
                         create_voice_record)


# Create your tests here.
class FileImportTestCase(TestCase):
    def test_properties(self):
        fi = FileImport(filename='cdr_data_MVNA_UK_EEL_SHAKA_111565_20230807130600.csv.gz')
        self.assertEqual('data', fi.data_type)
        self.assertEqual('MVNA_UK_EEL_SHAKA', fi.mvno)
        self.assertEqual(111565, fi.sequence_number)
        timezone = 'Europe/London'
        self.assertEqual(pytz.timezone(timezone).localize(datetime(2023, 8, 7, 13, 6, 0)), fi.datestamp)
        self.assertEqual('csv.gz', fi.extension)


class UsageAPITest(TestCase):
    def setUp(self):
        self.client.force_login(get_user_model().objects.create(is_superuser=True, is_staff=True, is_active=True))
        now = datetime.now()
        self.date_this_month = now.replace(day=1, hour=1, minute=1, second=1)
        self.start_of_this_month = self.date_this_month.replace(day=1, hour=0, minute=0, second=0)
        self.end_of_this_month = self.start_of_this_month + relativedelta(months=1)
        self.date_last_month = self.date_this_month - relativedelta(months=1)
        self.start_of_last_month = self.date_last_month.replace(day=1, hour=0, minute=0, second=0)
        self.end_of_last_month = self.start_of_last_month + relativedelta(months=1)

    def test_usage_respects_date_filter_narrow(self):
        create_data_record(self.date_last_month, 10)
        create_data_record(self.date_this_month, 20)
        self.assertEqual(20, self.get_usage('data', self.start_of_this_month, self.end_of_this_month))

    def test_usage_respects_msisdn(self):
        create_data_record(self.date_this_month, 10, msisdn='477123')
        create_data_record(self.date_this_month, 20, msisdn='4771234')
        self.assertEqual(20, self.get_usage('data', self.start_of_this_month, self.end_of_this_month, '4771234'))

    def test_usage_respects_date_filter_wide(self):
        create_data_record(self.date_last_month, 10)
        create_data_record(self.date_this_month, 20)
        create_data_record(self.date_this_month, 30)
        self.assertEqual(60, self.get_usage('data', self.start_of_last_month, self.end_of_this_month))

    def test_usage_handles_data(self):
        create_data_record(self.date_this_month, 20)
        self.assertEqual(20, self.get_usage('data', self.start_of_this_month, self.end_of_this_month))

    def test_usage_handles_sms(self):
        create_sms_record(self.date_this_month)
        create_sms_record(self.date_this_month)
        self.assertEqual(2, self.get_usage('sms', self.start_of_this_month, self.end_of_this_month))

    def test_usage_handles_voice(self):
        create_voice_record(self.date_this_month, 5)
        create_voice_record(self.date_this_month, 15)
        self.assertEqual(20, self.get_usage('voice', self.start_of_this_month, self.end_of_this_month))

    def get_usage(self, data_type, start, end, msisdn=DEFAULT_MSISDN):
        start_str = start.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end.strftime('%Y-%m-%d %H:%M:%S')
        resp = self.client.get(f'/api/v1/usage/{msisdn}/?data_type={data_type}&start_date={start_str}&end_date={end_str}')
        if resp.status_code % 500 == 0:
            raise RuntimeError(resp.json())
        return resp.json()['total_usage']

    def get_msisdns_with_data_over_threshold(self, start, end, threshold):
        start_str = start.strftime('%Y-%m-%d %H:%M:%S')
        end_str = end.strftime('%Y-%m-%d %H:%M:%S')
        resp = self.client.get(f'/api/v1/over-threshold-report/data/?start_date={start_str}&end_date={end_str}&threshold={threshold}')
        if resp.status_code % 500 == 0:
            raise RuntimeError(resp.json())
        return resp.json()

    def test_annotated_usage(self):
        create_data_record(self.date_this_month, 5)
        create_data_record(self.date_this_month, 15)
        results = self.get_msisdns_with_data_over_threshold(self.start_of_this_month, self.end_of_this_month, 10)
        self.assertEqual(1, len(results))
        self.assertEqual(DEFAULT_MSISDN, results[0]['msisdn'])
        self.assertEqual(20, results[0]['total_usage'])
