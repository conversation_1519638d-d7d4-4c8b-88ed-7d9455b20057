from datetime import timedelta

import pytz

from core.models import MVNO, SMSCDR, DataCDR, VoiceCDR

DEFAULT_MSISDN = '4471231231'


def lonify(datetime):
    return pytz.timezone('Europe/London').localize(datetime)


def _record_id_generator():
    x = 0
    while True:
        x += 1
        yield x

record_id_generator = _record_id_generator()

def get_or_create_mvno():
    mvno = MVNO.objects.all().first()
    if not mvno:
        mvno = MVNO.objects.create(id='test-mvno')
    return mvno


def create_voice_record(datetime, usage, msisdn=DEFAULT_MSISDN):
    mvno = get_or_create_mvno()
    return VoiceCDR.objects.create(
        record_id=next(record_id_generator),
        msisdn=msisdn,
        imsi='imsi',
        subscriber_id='sub.id',
        cos='cos',
        mvno=mvno,
        call_type=VoiceCDR.CallType.MOC,
        anumber=msisdn,
        bnumber=msisdn[::-1],
        prefix='prefix',
        destination='dest',
        location_zone='loczone',
        location_code='loccode',
        country_code='GBR',
        setup_time=lonify(datetime-timedelta(seconds=usage+1)),
        answer_time=lonify(datetime-timedelta(seconds=usage)),
        disconnect_time=lonify(datetime),
        duration=usage,
        filename='filename.csv.gz')

def create_data_record(datetime, usage, msisdn=DEFAULT_MSISDN):
    mvno = get_or_create_mvno()
    return DataCDR.objects.create(
        record_id=next(record_id_generator),
        msisdn=msisdn,
        imsi='imsi',
        subscriber_id='sub.id',
        cos='cos',
        mvno=mvno,
        call_type=DataCDR.CallType.MOG,
        apn='apn',
        location_zone='loczone',
        location_code='loccode',
        country_code='GBR',
        event_time=lonify(datetime),
        usu=usage,
        filename='filename.csv.gz')

def create_sms_record(datetime, msisdn=DEFAULT_MSISDN):
    print('Creating sms')
    mvno = get_or_create_mvno()
    return SMSCDR.objects.create(
        record_id=next(record_id_generator),
        msisdn=msisdn,
        imsi='imsi',
        subscriber_id='sub.id',
        cos='cos',
        mvno=mvno,
        call_type=SMSCDR.CallType.MOS,
        anumber=msisdn,
        bnumber=msisdn[::-1],
        prefix='prefix',
        destination='dest',
        location_zone='loczone',
        location_code='loccode',
        country_code='GBR',
        event_time=lonify(datetime),
        filename='filename.csv.gz')
