import logging
import requests
from django.conf import settings

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def send_slack_message(msg):
    if not settings.DEBUG:
        try:
            slack_webhook = '*********************************************************************************'
            print('To slack:', msg)
            logger.info('To slack: %s', msg)
            requests.post(slack_webhook, headers={'Content-type': 'application/json'}, json={"text":msg}, timeout=5)
        except: #pylint: disable=bare-except
            pass

def send_debug_slack_message(msg):
    print('To debug slack:', msg)
    if not settings.DEBUG:
        try:
            slack_webhook = '*********************************************************************************'
            logger.info('To debug slack: %s', msg)
            requests.post(slack_webhook, headers={'Content-type': 'application/json'}, json={"text":msg}, timeout=5)
        except: #pylint: disable=bare-except
            pass
