import logging
from datetime import datetime

import pytz
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.core.management import call_command
from django.conf import settings
from rest_framework import permissions
from rest_framework.response import Response
from rest_framework.views import APIView

from core import serializers
from provider.actions import perform_all_actions
from provider.polling import poll_all

from .usage import calculate_usage, msisdns_with_data_usage_over_threshold, aggregate_usage, calculate_eu_days

lon = pytz.timezone('Europe/London')

logger = logging.getLogger()


def parse_as_london_timezone(datestring):
    return lon.localize(datetime.strptime(datestring, "%Y-%m-%d %H:%M:%S"))


# Create your views here.
def test_view(_):
    logger.info('View called, this is debug log')
    logger.info('View called, this is info log')
    logger.info('View called, this is error log')
    print('this is print')
    return HttpResponse('test')

def test_error(_):
    raise RuntimeError()


class TokenOrAdminPermission(permissions.IsAdminUser):
    def has_permission(self, request, view):
        if super().has_permission(request, view):
            return True

        authorization_header = request.headers.get('Authorization')
        if authorization_header and authorization_header.startswith('Bearer '):
            token = authorization_header.split(' ')[1]

            expected_token = getattr(settings, 'NEXUS_API_TOKEN', None)
            if expected_token and token and token == expected_token:
                return True

        return False


class CheckUsage(APIView):
    permission_classes = [TokenOrAdminPermission]
    serializer_class = serializers.UsageSerializer

    def get(self, request, *, msisdn):
        msisdn = self.kwargs['msisdn']
        start_date_str = self.request.query_params.get('start_date')
        end_date_str = self.request.query_params.get('end_date')
        start_date = parse_as_london_timezone(start_date_str) if start_date_str else lon.localize(datetime.now().replace(day=1, hour=0, minute=0, second=0))
        end_date = parse_as_london_timezone(end_date_str) if end_date_str else lon.localize(datetime.now())
        data_type = self.request.query_params.get('data_type', 'data')
        return Response(self.serializer_class(calculate_usage(msisdn, data_type, start_date, end_date)).data)


class EuRoamingDays(APIView):
    permission_classes = [TokenOrAdminPermission]
    serializer_class = serializers.UsageSerializer

    def get(self, request, *, msisdn):
        msisdn = self.kwargs['msisdn']
        start_date_str = self.request.query_params.get('start_date')
        end_date_str = self.request.query_params.get('end_date')
        start_date = parse_as_london_timezone(start_date_str) if start_date_str else lon.localize(datetime.now().replace(day=1, hour=0, minute=0, second=0))
        end_date = parse_as_london_timezone(end_date_str) if end_date_str else lon.localize(datetime.now())
        return JsonResponse({'eu_days': calculate_eu_days(msisdn, start_date, end_date)})


class MSISDNSWithDataOverThreshold(APIView):
    permission_classes = [TokenOrAdminPermission]
    serializer_class = serializers.UsageSerializer

    def get(self, request):
        start_date_str = self.request.query_params.get('start_date')
        end_date_str = self.request.query_params.get('end_date')
        threshold = int(self.request.query_params.get('threshold'))
        start_date = parse_as_london_timezone(start_date_str) if start_date_str else lon.localize(datetime.now().replace(day=1, hour=0, minute=0, second=0))
        end_date = parse_as_london_timezone(end_date_str) if end_date_str else lon.localize(datetime.now())
        return Response(self.serializer_class(msisdns_with_data_usage_over_threshold(start_date, end_date, threshold), many=True).data)


class AggregateUsage(APIView):
    permission_classes = [TokenOrAdminPermission]
    serializer_class = serializers.AggregationSerializer

    def get(self, request):
        start_date_str = self.request.query_params.get('start_date')
        end_date_str = self.request.query_params.get('end_date')
        start_date = parse_as_london_timezone(start_date_str) if start_date_str else lon.localize(datetime.now().replace(day=1, hour=0, minute=0, second=0))
        end_date = parse_as_london_timezone(end_date_str) if end_date_str else lon.localize(datetime.now())
        data_type = self.request.query_params.get('data_type', 'data')
        msisdns = self.request.query_params.getlist('msisdns')

        aggregation_period = self.request.query_params.get('aggregation_period', 'day')

        return Response(self.serializer_class(aggregate_usage(start_date, end_date, data_type, aggregation_period, msisdns), many=True).data)

def custom_token_required(view_func):
    def wrapped_view(request, *args, **kwargs):
        authorization_header = request.headers.get('Authorization')

        # Ensure Authorization header is present and starts with 'Bearer '
        if not authorization_header or not authorization_header.startswith('Bearer '):
            return JsonResponse({'error': 'Invalid or missing Bearer token'}, status=401)

        # Extract the token from the Authorization header
        token = authorization_header.split(' ')[1]

        # Replace 'your_custom_token' with the actual token you want to use
        if token != settings.LAMBDA_API_TOKEN:
            return JsonResponse({'error': 'Unauthorized'}, status=401)

        return view_func(request, *args, **kwargs)

    return wrapped_view

@csrf_exempt
@require_POST
@custom_token_required
def run_airtable_import(request):
    start_datetime = request.POST.get('start_datetime')
    end_datetime = request.POST.get('end_datetime')
    data_type = request.POST.get('data_type')

    try:
        # Call your management command
        call_command('update_records_in_airtable', start_datetime=start_datetime, end_datetime=end_datetime, data_type=data_type)
        call_command('look_for_roaming_fup')
    except Exception as e:  # pylint: disable=broad-exception-caught
        logger.exception('Error updating records in airtable', exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'message': 'Done'})


@csrf_exempt
@require_POST
@custom_token_required
def cleanup_cdr_files(request):
    try:
        call_command('cleanup_cdr_files')
    except Exception as e:  # pylint: disable=broad-exception-caught
        logger.exception('Error cleaning up cdr files', exc_info=True)
        return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'message': 'Done'})


@csrf_exempt
@require_POST
@custom_token_required
def run_provider_actions(request):
    perform_all_actions()
    return JsonResponse({'message': 'Done'})


@csrf_exempt
@require_POST
@custom_token_required
def run_poller(request):
    poll_all()
    return JsonResponse({'message': 'Done'})


def blank_homepage(request):
    return HttpResponse('')
