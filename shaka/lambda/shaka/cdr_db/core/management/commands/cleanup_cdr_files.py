import os
import logging
from collections import defaultdict
import pathlib
from django.core.management.base import BaseCommand
from paramiko import SFTPClient, Transport
from core.models import FileImport


logger = logging.getLogger()
logger.setLevel(logging.INFO)


class Command(BaseCommand):
    help = 'Clean up already imported cdr files in sftp'

    def handle(self, *args, **options):
        files_to_cleanup = FileImport.objects.filter(cleaned_up=False, status=FileImport.Status.DONE).order_by('-started')
        if files_to_cleanup:
            by_folder = defaultdict(list)
            for file_import in files_to_cleanup:
                by_folder[file_import.data_type].append(file_import)
            sftp_dir = pathlib.Path(os.environ['SFTP_DIR'])
            with Transport((os.environ['SFTP_HOST'], int(os.environ['SFTP_PORT']))) as transport:
                transport.connect(username=os.environ['SFTP_USERNAME'], password=os.environ['SFTP_PASSWORD'])
                with SFTPClient.from_transport(transport) as sftp:
                    for folder, file_imports in by_folder.items():
                        source_path = sftp_dir / folder
                        remote_files = set(sftp.listdir(str(source_path)))
                        for file_import_to_cleanup in file_imports:
                            print('considering', file_import_to_cleanup.filename)
                            if file_import_to_cleanup.filename in remote_files:
                                logger.info('Cleaning up %s', file_import_to_cleanup.filename)
                                dest_path = sftp_dir / folder / 'imported'
                                logger.info('Renaming %s to %s', str(source_path / file_import_to_cleanup.filename), str(dest_path / file_import_to_cleanup.filename))
                                sftp.rename(str(source_path / file_import_to_cleanup.filename), str(dest_path / file_import_to_cleanup.filename))
            print('marking all as done')
            files_to_cleanup.update(cleaned_up=True)
