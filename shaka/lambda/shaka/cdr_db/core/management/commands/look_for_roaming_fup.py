import os
from datetime import datetime, timedelta
import logging
from django.core.management.base import BaseCommand
from core.models import GammaDataCDR
from core.slack import send_debug_slack_message
from provider.models import ProviderAction
from provider.clients import GammaClient


class Command(BaseCommand):
    help = 'Look for roaming FUP'

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        start_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        next_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0) + timedelta(days=33)
        end_date = next_month.replace(day=1)
        relevant = GammaDataCDR.objects.filter(roaming_zone=GammaDataCDR.RoamingZones.E).date_filter(start_date, end_date).group_by_msisdn().annotate_usage()
        fifteen_gb = 15 * 1024 * 1024 * 1024
        eighteen_gb = 18 * 1024 * 1024 * 1024
        for obj in relevant:
            msisdn = obj['msisdn']
            usage = obj['total_usage']
            if usage > fifteen_gb:
                send_debug_slack_message(f'Roaming FUP for {msisdn}: {usage / (1024 * 1024 * 1024)} GB')
            if usage > eighteen_gb:
                service = GammaClient().get_service_by_msisdn(msisdn)
                iccid = service['sim']['id']
                if 'networkServices' in service:
                    ProviderAction.objects.create(
                        action='gamma-real-bar-data',
                        data_payload={
                            'sim_serial': iccid,
                            'callback_url': 'https://nexus.shaka.tel/webhooks/temp/'
                        }
                    )
