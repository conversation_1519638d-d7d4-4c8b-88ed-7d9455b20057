# pylint: disable=duplicate-code
# pylint: disable=wrong-import-position
import os
from datetime import datetime, timedelta
import logging
import boto3
from pyairtable import Table
from django.core.management.base import BaseCommand
from core.models import VoiceCDR, DataCDR, SMSCDR


class AirtableClient:
    def __init__(self, table_name, field_name, model_converter):
        self.table_name = table_name
        self.field_name = field_name
        self.model_converter = model_converter
        self.table = Table(os.environ.get('AIRTABLE_PAT'), os.environ.get('AIRTABLE_BASE_ID'), self.table_name)

    def get_record_ids(self, start_datetime, end_datetime):
        start_iso = start_datetime.isoformat()
        end_iso = end_datetime.isoformat()
        # Actually exclusive both ends, shouldn't really matter because you'd have to land right on a record
        # and airtable will be going away soon
        filter_formula = f"AND(IS_AFTER({{{self.field_name}}}, '{start_iso}'), IS_BEFORE({{{self.field_name}}}, '{end_iso}'))"
        records_within_range = self.table.all(formula=filter_formula)
        return [f['fields']['RecordId'] for f in records_within_range]

    def upsert_records(self, model_objects):
        dynamodb_client = boto3.client('dynamodb')
        recs = [
            r for r in ({'fields': self.model_converter(dynamodb_client, obj)} for obj in model_objects)
            if r['fields']['current_subscription_id'] and r['fields']['current_subscription_id'][0] != None
        ]

        self.table.batch_upsert(recs, ['RecordId'])


def get_subscription_id(dynamodb_client, msisdn):
    dynamodb_airtable_active_subscriptions = os.environ['DYNAMODB_AIRTABLE_ACTIVE_SUBSCRIPTIONS']
    response = dynamodb_client.get_item(
        TableName=dynamodb_airtable_active_subscriptions,
        Key={'msisdn': {'S': msisdn}},
        ProjectionExpression='subscription_id'
    )
    item = response.get('Item')
    if item:
        subscription_id = item.get('subscription_id', {}).get('S')
        return subscription_id
    return None


def convert_data_cdr(dynamodb_client, obj):
    return {
        'RecordId': str(obj.record_id),
        'MSISDN': obj.msisdn,
        'IMSI': obj.imsi,
        'CallType': obj.call_type,
        'LocationZoneName': obj.country_code,
        'EventTime': obj.event_time.strftime('%Y-%m-%d %H:%M:%S'),
        'USU': obj.usu,
        'current_subscription_id': [get_subscription_id(dynamodb_client, obj.msisdn)]
    }

def convert_voice_cdr(dynamodb_client, obj):
    return {
        'RecordId': str(obj.record_id),
        'MSISDN': obj.msisdn,
        'IMSI': obj.imsi,
        'CallType': obj.call_type,
        'Anumber': obj.anumber,
        'Bnumber': obj.bnumber,
        'Rnumber': obj.rnumber,
        'ISO3': obj.country_code,
        'EventTime': obj.setup_time.strftime('%Y-%m-%d %H:%M:%S'),
        'Duration': obj.duration,
        'current_subscription_id': [get_subscription_id(dynamodb_client, obj.msisdn)]
    }

def convert_sms_cdr(dynamodb_client, obj):
    return {
        'RecordId': str(obj.record_id),
        'MSISDN': obj.msisdn,
        'IMSI': obj.imsi,
        'CallType': obj.call_type,
        'Anumber': obj.anumber,
        'Bnumber': obj.bnumber,
        'ISO3': obj.country_code,
        'EventTime': obj.event_time.strftime('%Y-%m-%d %H:%M:%S'),
        'current_subscription_id': [get_subscription_id(dynamodb_client, obj.msisdn)]
    }

class DataAirtableClient(AirtableClient):
    def __init__(self):
        super().__init__(os.environ.get('AIRTABLE_TABLE_NAME_DATA'), 'EventTime', convert_data_cdr)

class VoiceAirtableClient(AirtableClient):
    def __init__(self):
        super().__init__(os.environ.get('AIRTABLE_TABLE_NAME_VOICE'), 'EventTime', convert_voice_cdr)

class SMSAirtableClient(AirtableClient):
    def __init__(self):
        super().__init__(os.environ.get('AIRTABLE_TABLE_NAME_SMS'), 'EventTime', convert_sms_cdr)

class Command(BaseCommand):
    help = 'Update records in Airtable based on specified parameters'

    def add_arguments(self, parser):
        parser.add_argument('--start-datetime', type=str, help='Start datetime in ISO format')
        parser.add_argument('--end-datetime', type=str, help='End datetime in ISO format')
        parser.add_argument('--data-type', type=str, help='Data type: voice, data, or sms')

    def handle(self, *args, **options):
        start_datetime = datetime.fromisoformat(options['start_datetime'])
        end_datetime = datetime.fromisoformat(options['end_datetime'])
        data_type = options['data_type']
        AirtableClass = {
            'data': DataAirtableClient,
            'voice': VoiceAirtableClient,
            'sms': SMSAirtableClient
        }[data_type]
        CDRModel = {
            'data': DataCDR,
            'voice': VoiceCDR,
            'sms': SMSCDR
        }[data_type]

        airtable_client = AirtableClass()
        airtable_record_ids = airtable_client.get_record_ids(start_datetime, end_datetime)
        records_to_insert = CDRModel.objects.exclude(record_id__in=airtable_record_ids).date_filter(start_datetime, end_datetime)
        airtable_client.upsert_records(records_to_insert)
