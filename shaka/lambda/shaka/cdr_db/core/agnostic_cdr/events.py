import datetime

import attrs


@attrs.frozen
class BaseAgnosticCDREvent:
    msisdn: str
    event_time: datetime.datetime


@attrs.frozen
class AgnosticDataCDREvent(BaseAgnosticCDREvent):
    data_usage_bytes: int
    roaming_zone: str


@attrs.frozen
class AgnosticVoiceCDREvent(BaseAgnosticCDREvent):
    duration_seconds: int


@attrs.frozen
class AgnosticSMSCDREvent(BaseAgnosticCDREvent):
    quantity: int
