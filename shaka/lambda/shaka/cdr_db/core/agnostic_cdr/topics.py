from typing import TypeAlias

from django.conf import settings

from core.topic import topic

from . import events


AgnosticEvent: TypeAlias = events.AgnosticVoiceCDREvent | events.AgnosticDataCDREvent | events.AgnosticSMSCDREvent

agnostic_cdr_generated: topic.Topic[AgnosticEvent] = topic.Topic(
    events_handlers=(
        topic.EventHandlers(events.AgnosticVoiceCDREvent, ()),
        topic.EventHandlers(events.AgnosticDataCDREvent, (print,)),
        topic.EventHandlers(events.AgnosticSMSCDREvent, ()),
    ),
    publisher=topic.SNSPublisher(arn=settings.AGNOSTIC_CDR_GENERATED_TOPIC_ARN, subject='')
)
