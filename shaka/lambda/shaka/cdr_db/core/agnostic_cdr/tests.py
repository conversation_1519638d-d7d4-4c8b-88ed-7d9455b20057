import unittest

from core.agnostic_cdr import topics, events


class TestAgnosticCDRGeneratedTopic(unittest.TestCase):
    def test_can_handle_agnostic_data_event(self):
        test_event = {
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "data_usage_bytes": 1024,
            "roaming_zone": "A",
            "__type": events.AgnosticDataCDREvent.__name__,
            "__published_at": 1735689600.0,
        }

        # This topic currently has no handler logic, so we just check that it doesn't throw an exception
        topics.agnostic_cdr_generated.handle(test_event)

    def test_can_handle_agnostic_voice_event(self):
        test_event = {
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "duration_seconds": 60,
            "__type": events.AgnosticVoiceCDREvent.__name__,
            "__published_at": 1735689600.0,
        }

        # This topic currently has no handler logic, so we just check that it doesn't throw an exception
        topics.agnostic_cdr_generated.handle(test_event)

    def test_can_handle_agnostic_sms_event(self):
        test_event = {
            "msisdn": "1234",
            "event_time": "2025-01-01T00:00:00",
            "quantity": 1,
            "__type": events.AgnosticSMSCDREvent.__name__,
            "__published_at": 1735689600.0,
        }

        # This topic currently has no handler logic, so we just check that it doesn't throw an exception
        topics.agnostic_cdr_generated.handle(test_event)
