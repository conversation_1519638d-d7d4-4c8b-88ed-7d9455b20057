from django.contrib import admin

from .models import MVNO, SMSCDR, DataCDR, FileImport, VoiceCDR, GammaFileImport, GammaDataCDR, GammaVoiceCDR, GammaSMSCDR


@admin.register(MVNO)
class MVNOAdmin(admin.ModelAdmin):
    search_fields = ['id']
    list_display = ['id']

@admin.register(VoiceCDR)
class VoiceCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'imsi', 'subscriber_id']
    list_display = ['record_id', 'msisdn', 'imsi', 'subscriber_id', 'call_type', 'setup_time', 'answer_time', 'disconnect_time']

@admin.register(DataCDR)
class DataCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'imsi', 'subscriber_id']
    list_display = ['record_id', 'msisdn', 'imsi', 'subscriber_id', 'call_type', 'apn', 'event_time', 'usu']

@admin.register(SMSCDR)
class SMSCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'imsi', 'subscriber_id']
    list_display = ['record_id', 'msisdn', 'imsi', 'subscriber_id', 'call_type', 'anumber', 'bnumber', 'event_time']

@admin.register(FileImport)
class FileImportAdmin(admin.ModelAdmin):
    search_fields = ['filename', 'status']
    list_display = ['filename', 'status', 'started', 'last_updated']
    list_filter = ['status']

@admin.register(GammaFileImport)
class GammaFileImportAdmin(admin.ModelAdmin):
    search_fields = ['filename', 'status']
    list_display = ['filename', 'status', 'started', 'last_updated']
    list_filter = ['status']

@admin.register(GammaDataCDR)
class GammaDataCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'event_time']
    list_display = ['record_id', 'msisdn', 'call_type', 'event_time', 'data_usage_bytes', 'country_of_origin']

@admin.register(GammaVoiceCDR)
class GammaVoiceCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'event_time']
    list_display = ['record_id', 'msisdn', 'call_type', 'event_time', 'duration_seconds', 'country_of_origin']

@admin.register(GammaSMSCDR)
class GammaSMSCDRAdmin(admin.ModelAdmin):
    search_fields = ['record_id', 'msisdn', 'event_time']
    list_display = ['record_id', 'msisdn', 'call_type', 'event_time', 'quantity', 'country_of_origin']
