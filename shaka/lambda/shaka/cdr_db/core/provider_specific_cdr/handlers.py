from core.agnostic_cdr import topics as agnostic_topics

from . import events, converters


def convert_and_publish_gamma_voice_cdr(event: events.GammaVoiceCDREvent) -> None:
    agnostic_cdr = converters.gamma_voice_agnostic_converter.convert(event)
    agnostic_topics.agnostic_cdr_generated.publish(agnostic_cdr)


def convert_and_publish_gamma_data_cdr(event: events.GammaDataCDREvent) -> None:
    agnostic_cdr = converters.gamma_data_agnostic_converter.convert(event)
    agnostic_topics.agnostic_cdr_generated.publish(agnostic_cdr)


def convert_and_publish_gamma_sms_cdr(event: events.GammaSMSCDREvent) -> None:
    agnostic_cdr = converters.gamma_sms_agnostic_converter.convert(event)
    agnostic_topics.agnostic_cdr_generated.publish(agnostic_cdr)
