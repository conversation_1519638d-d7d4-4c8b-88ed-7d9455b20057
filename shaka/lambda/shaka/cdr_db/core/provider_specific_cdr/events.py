import datetime
from decimal import Decimal

import attrs


@attrs.frozen
class BaseGammaCDREvent:
    filename: str
    call_type: str | None
    call_cause_definition_required: str | None
    customer_identifier: str | None
    non_charged_party: str | None
    call_date: str | None
    call_time: str | None
    duration: int | None
    bytes_transmitted: int | None
    bytes_received: int | None
    description: str | None
    chargecode: str | None
    time_band: str | None
    salesprice: Decimal | None
    salesprice_pre_bundle: str | None
    extension: str | None
    ddi: str | None
    grouping_id: str | None
    call_class_feature: str | None
    carrier: str | None
    recording: str | None
    vat: str | None
    country_of_origin: str | None
    network: str | None
    retail_tariff_code: str | None
    remote_network: str | None
    apn: str | None
    diverted_number: str | None
    ring_time: str | None
    record_id: str | None
    currency: str | None
    caller_line_identity: str | None
    network_access_reference: str | None
    ngcs_access_charge: str | None
    ngcs_service_charge: str | None
    total_bytes_transferred: int | None
    user_id: str | None
    onward_billing_reference: str | None
    contract_name: str | None
    bundle_name: str | None
    bundle_allowance: int | None
    discount_reference: str | None
    routing_code: str | None

    # Additional fields
    msisdn: str
    event_time: datetime.datetime



@attrs.frozen
class GammaDataCDREvent(BaseGammaCDREvent):
    data_usage_bytes: int
    roaming_zone: str


@attrs.frozen
class GammaVoiceCDREvent(BaseGammaCDREvent):
    duration_seconds: int


@attrs.frozen
class GammaSMSCDREvent(BaseGammaCDREvent):
    quantity: int
