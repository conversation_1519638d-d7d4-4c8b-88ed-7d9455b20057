from django.urls import path

from core import views

urlpatterns = [
    path('', views.blank_homepage, name='homepage'),
    path('test/', views.test_view, name='test_view'),
    path('test-error/', views.test_error, name='test_error'),
    path('api/v1/usage/<str:msisdn>/', views.CheckUsage.as_view(), name='usage-api'),
    path('api/v1/eu-roaming-days/<str:msisdn>/', views.EuRoamingDays.as_view(), name='eu-roaming-api'),
    path('api/v1/aggregate-usage/', views.AggregateUsage.as_view(), name='aggregate-usage-api'),
    path('api/v1/over-threshold-report/data/', views.MSISDNSWithDataOverThreshold.as_view(), name='over-threshold-report-data'),
    path('backend-api/run-airtable-import/', views.run_airtable_import, name='run_airtable_import'),
    path('backend-api/cleanup-cdr-files/', views.cleanup_cdr_files, name='cleanup_cdr_files'),
    path('backend-api/run-provider-actions/', views.run_provider_actions, name='run_provider_actions'),
    path('backend-api/run-poller/', views.run_poller, name='run_poller'),
]
