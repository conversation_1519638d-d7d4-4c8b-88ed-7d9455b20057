import dataclasses
from datetime import datetime
import pytz
from django.db.models.functions import <PERSON>run<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from core.models import SMSCDR, DataCDR, VoiceCDR, GammaDataCDR, GammaVoiceCDR, GammaSMSCDR


@dataclasses.dataclass
class Usage:
    msisdn: str
    data_type: str
    start_date: datetime
    end_date: datetime
    total_usage: int

@dataclasses.dataclass
class AggregateUsage:
    start_date: datetime
    end_date: datetime
    total_usage: int
    data_type: str
    aggregation_period: str
    date: datetime


def calculate_usage(msisdn, data_type, start_date, end_date):
    model = {
        'data': GammaDataCDR,
        'voice': GammaVoiceCDR,
        'sms': GammaSMSCDR
    }[data_type]
    if not model.objects.filter(msisdn=msisdn).exists():
        model = {
            'data': DataCDR,
            'voice': VoiceCDR,
            'sms': SMSCDR
        }[data_type]
    return Usage(msisdn, data_type, start_date, end_date, model.objects.filter(msisdn=msisdn).date_filter(start_date, end_date).sum_usage())


def aggregate_usage(start_date, end_date, data_type, aggregation_period, msisdns):
    aggregations = []
    for modelset in [
            {
                'data': GammaDataCDR,
                'voice': GammaVoiceCDR,
                'sms': GammaSMSCDR
            },
            {
                'data': DataCDR,
                'voice': VoiceCDR,
                'sms': SMSCDR
            }]:
        model = modelset[data_type]
        queryset = model.objects.date_filter(start_date, end_date).filter(msisdn__in=msisdns)
        if aggregation_period == 'day':
            trunc = TruncDay
        elif aggregation_period == 'month':
            trunc = TruncMonth
        elif aggregation_period == 'year':
            trunc = TruncYear

        fr = pytz.timezone('Europe/Paris')

        queryset = queryset.annotate(date=trunc(model.time_field, tzinfo=fr)).values('date').annotate_usage()
        aggregations += [AggregateUsage(start_date, end_date, obj['total_usage'], data_type, aggregation_period, obj['date']) for obj in queryset]
    return aggregations


def msisdns_with_data_usage_over_threshold(start_date, end_date, threshold):
    return [Usage(obj['msisdn'], 'data', start_date, end_date, obj['total_usage']) for obj in DataCDR.objects.date_filter(start_date, end_date).group_by_msisdn().annotate_usage().filter(total_usage__gt=threshold)] + [Usage(obj['msisdn'], 'data', start_date, end_date, obj['total_usage']) for obj in GammaDataCDR.objects.date_filter(start_date, end_date).group_by_msisdn().annotate_usage().filter(total_usage__gt=threshold)]

def calculate_eu_days(msisdn, start_date, end_date):
    return GammaDataCDR.objects.filter(msisdn=msisdn, event_time__gte=start_date, event_time__lt=end_date, roaming_zone=GammaDataCDR.RoamingZones.E).annotate(day=TruncDay('event_time')).values('day').distinct().count()
