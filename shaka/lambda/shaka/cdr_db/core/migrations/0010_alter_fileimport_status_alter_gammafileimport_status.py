# Generated by Django 4.2.7 on 2024-09-22 21:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0009_gammafileimport_gammavoicecdr_gammasmscdr_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='fileimport',
            name='status',
            field=models.CharField(choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('errored', 'Errored'), ('skipped_strange', 'Skipped (strange)'), ('skipped_ignorable', 'Skipped (ignorable)')], db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='gammafileimport',
            name='status',
            field=models.CharField(choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('errored', 'Errored'), ('skipped_strange', 'Skipped (strange)'), ('skipped_ignorable', 'Skipped (ignorable)')], db_index=True, max_length=100),
        ),
    ]
