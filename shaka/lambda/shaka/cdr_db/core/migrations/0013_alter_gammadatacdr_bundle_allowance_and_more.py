# Generated by Django 4.2.7 on 2024-09-23 01:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0012_gammadatacdr_filename_gammasmscdr_filename_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='gammadatacdr',
            name='bundle_allowance',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammadatacdr',
            name='bytes_received',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammadatacdr',
            name='bytes_transmitted',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammadatacdr',
            name='data_usage_bytes',
            field=models.BigIntegerField(),
        ),
        migrations.AlterField(
            model_name='gammadatacdr',
            name='duration',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='gammadatacdr',
            name='total_bytes_transferred',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='bundle_allowance',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='bytes_received',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='bytes_transmitted',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='duration',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='quantity',
            field=models.BigIntegerField(),
        ),
        migrations.AlterField(
            model_name='gammasmscdr',
            name='total_bytes_transferred',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='bundle_allowance',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='bytes_received',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='bytes_transmitted',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='duration',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='duration_seconds',
            field=models.BigIntegerField(),
        ),
        migrations.AlterField(
            model_name='gammavoicecdr',
            name='total_bytes_transferred',
            field=models.BigIntegerField(blank=True, null=True),
        ),
    ]
