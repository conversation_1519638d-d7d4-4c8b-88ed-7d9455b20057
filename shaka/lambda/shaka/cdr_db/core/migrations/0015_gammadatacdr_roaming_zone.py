# Generated by Django 4.2.7 on 2024-12-17 14:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0014_alter_gammadatacdr_filename_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='gammadatacdr',
            name='roaming_zone',
            field=models.CharField(choices=[('a', 'A (UK)'), ('e', 'E (EU)'), ('b', 'B'), ('c', 'C'), ('d', 'D')], db_index=True, default='a', max_length=255),
        ),
    ]
