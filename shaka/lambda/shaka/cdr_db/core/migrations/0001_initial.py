# Generated by Django 4.2.7 on 2023-12-02 21:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='MVNO',
            fields=[
                ('id', models.CharField(max_length=100, primary_key=True, serialize=False)),
            ],
        ),
        migrations.CreateModel(
            name='VoiceCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_id', models.BigIntegerField()),
                ('msisdn', models.CharField(max_length=10)),
                ('imsi', models.CharField(max_length=10)),
                ('subscriber_id', models.CharField(max_length=100)),
                ('cos', models.CharField(max_length=100)),
                ('call_type', models.CharField(choices=[('MOC', 'MOC'), ('MTC', 'MTC'), ('MFC', 'MFC'), ('ROC', 'ROC'), ('RTC', 'RTC'), ('RFC', 'RFC')], max_length=20)),
                ('bearer', models.CharField(blank=True, default='', max_length=100)),
                ('anumber', models.CharField(max_length=100)),
                ('bnumber', models.CharField(max_length=100)),
                ('rnumber', models.CharField(blank=True, default='', max_length=100)),
                ('prefix', models.CharField(max_length=10)),
                ('destination', models.CharField(max_length=100)),
                ('location_zone_name', models.CharField(max_length=100)),
                ('location_code', models.CharField(max_length=100)),
                ('country_code', models.CharField(max_length=3)),
                ('mcc', models.IntegerField(blank=True, null=True)),
                ('mnc', models.IntegerField(blank=True, null=True)),
                ('cell_id', models.CharField(blank=True, default='', max_length=100)),
                ('setup_time', models.DateTimeField()),
                ('answer_time', models.DateTimeField()),
                ('disconnect_time', models.DateTimeField()),
                ('duration', models.IntegerField()),
                ('mvno', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.mvno')),
            ],
        ),
    ]
