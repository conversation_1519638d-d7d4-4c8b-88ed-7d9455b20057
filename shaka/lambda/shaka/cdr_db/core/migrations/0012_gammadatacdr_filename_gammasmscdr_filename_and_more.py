# Generated by Django 4.2.7 on 2024-09-23 00:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0011_gammadatacdr_call_type_gammasmscdr_call_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='gammadatacdr',
            name='filename',
            field=models.CharField(db_index=True, default=None, max_length=100, unique=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='gammasmscdr',
            name='filename',
            field=models.CharField(db_index=True, default=None, max_length=100, unique=True),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='gammavoicecdr',
            name='filename',
            field=models.CharField(db_index=True, default=None, max_length=100, unique=True),
            preserve_default=False,
        ),
    ]
