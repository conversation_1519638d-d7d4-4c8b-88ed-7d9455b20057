# Generated by Django 4.2.7 on 2024-01-18 01:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_datacdr_core_datacd_msisdn_684812_idx_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='fileimport',
            name='cleaned_up',
            field=models.BooleanField(db_index=True, default=False),
        ),
        migrations.AlterField(
            model_name='fileimport',
            name='filename',
            field=models.CharField(db_index=True, max_length=100, unique=True),
        ),
    ]
