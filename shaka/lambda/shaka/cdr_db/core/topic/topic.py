from __future__ import annotations

import datetime
import decimal
import json
import time
from typing import TypeVar, Generic, Iterable, TypeAlias, Any, Callable, Iterator

import attrs
import boto3
import cattrs


def _structure_datetime(d: object, _: Any) -> datetime.datetime:
    if isinstance(d, datetime.datetime):
        return d
    elif isinstance(d, str):
        return datetime.datetime.fromisoformat(d)
    else:
        raise ValueError("Cannot convert to datetime")

def _structure_decimal(d: object, _: Any) -> decimal.Decimal:
    if isinstance(d, str | int | decimal.Decimal):
        return decimal.Decimal(d)
    else:
        raise ValueError("Cannot convert to decimal")


converter = cattrs.Converter()
converter.register_unstructure_hook(datetime.datetime, datetime.datetime.isoformat)
converter.register_unstructure_hook(decimal.Decimal, str)
converter.register_structure_hook(datetime.datetime, _structure_datetime)
converter.register_structure_hook(decimal.Decimal, _structure_decimal)


T_StructuredEvent = TypeVar('T_StructuredEvent')
UnstructuredEvent: TypeAlias = dict[Any, Any]


class NoHandlerFoundError(Exception):
    pass


class MisconfiguredEventError(Exception):
    pass


@attrs.frozen
class EventHandlers(Generic[T_StructuredEvent]):
    event_type: type[T_StructuredEvent]
    handlers: Iterable[Callable[[T_StructuredEvent], None]]


class Publisher:
    """
    A publisher publishes unstructured data.
    """
    def publish(self, unstructured_event: UnstructuredEvent) -> None:
        raise NotImplementedError


class SNSPublisher(Publisher):
    arn: str
    subject: str | None = None

    _client: boto3.client | None = None

    def __init__(self, arn: str, subject: str | None = None):
        self.arn = arn
        self.subject = subject

    def publish(self, unstructured_event: UnstructuredEvent) -> None:
        self._get_client().publish(
            TopicArn=self.arn,
            Message=json.dumps(unstructured_event),
            Subject=self.subject or "",
        )

    def _get_client(self) -> boto3.client:
        if not self._client:
            self._client = boto3.client("sns")
        return self._client


class Topic(Generic[T_StructuredEvent]):
    """
    A topic handles incoming events and publishes outgoing events.
    """
    events_handlers: Iterable[EventHandlers]
    publisher: Publisher

    _type_identifier: str = "__type"

    def __init__(self, events_handlers: Iterable[EventHandlers], publisher: Publisher):
        self.events_handlers = events_handlers
        self.publisher = publisher

    def handle(self, unstructured_event: UnstructuredEvent) -> None:
        event_type = self._determine_event_type(unstructured_event)
        event = converter.structure(unstructured_event, event_type)
        for event_handler in self._handlers_for_event(event):
            try:
                event_handler(event)
            except Exception as e:  # pylint: disable=broad-except,unused-variable
                # Log error
                pass

    def publish(self, event: T_StructuredEvent) -> None:
        unstructured_event = converter.unstructure(event)
        self.publisher.publish(self._wrap_event(unstructured_event, type(event)))

    def _handlers_for_event(self, event: T_StructuredEvent) -> Iterator[Callable[[T_StructuredEvent], None]]:
        for event_handlers in self.events_handlers:
            if isinstance(event, event_handlers.event_type):
                yield from event_handlers.handlers

    def _determine_event_type(self, unstructured_event: UnstructuredEvent) -> type[T_StructuredEvent]:
        event_type_name = unstructured_event.get(self._type_identifier)
        if not event_type_name:
            raise MisconfiguredEventError("Event is missing type identifier")
        for handled_type in self._handled_types():
            if handled_type.__name__ == event_type_name:
                return handled_type
        raise NoHandlerFoundError(f"No handler found for event type: {event_type_name}")

    def _handled_types(self) -> Iterable[type[T_StructuredEvent]]:
        return (handler.event_type for handler in self.events_handlers)

    def _wrap_event(self, unstructured_event: UnstructuredEvent, event_type: type[T_StructuredEvent]) -> UnstructuredEvent:
        return {**unstructured_event, self._type_identifier: event_type.__name__, "__published_at": time.time()}
