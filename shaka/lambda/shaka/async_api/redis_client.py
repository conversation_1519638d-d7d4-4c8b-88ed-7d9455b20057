import os
import redis
from redis.exceptions import RedisError


_redis_client = None


def get_redis_client() -> redis.Redis:
    """
    Get a Redis client instance with connection pooling.
    
    Returns:
        redis.Redis: Redis client instance
    """
    global _redis_client
    
    if _redis_client is None:
        _redis_client = redis.Redis(
            host=os.getenv("REDIS_HOST", "localhost"),
            port=int(os.getenv("REDIS_PORT", 6379)),
            db=0,
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
    
    return _redis_client


def test_redis_connection() -> bool:
    """
    Test the Redis connection.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        client = get_redis_client()
        return client.ping()
    except RedisError:
        return False
