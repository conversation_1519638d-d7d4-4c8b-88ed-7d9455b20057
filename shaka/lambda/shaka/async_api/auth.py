import os
import asyncpg
import logging
from typing import Optional

logger = logging.getLogger(__name__)
_db_pool = None

async def get_db_pool():
    global _db_pool
    if _db_pool is None:
        db_user = os.getenv('DB_USER', 'cdr_db')
        db_password = os.getenv('DB_PASSWORD', '')
        db_host = os.getenv('DB_HOST', 'localhost')
        database_url = os.getenv(
            'DATABASE_URL',
            f"postgresql://{db_user}:{db_password}@{db_host}/nexus"
        )
        try:
            _db_pool = await asyncpg.create_pool(
                database_url,
                min_size=1,
                max_size=10,
                command_timeout=5
            )
            logger.info("Database connection pool created")
        except Exception as e:
            logger.error(f"Failed to create database pool: {e}")
            raise

    return _db_pool

async def get_client_id_from_token(token: str) -> Optional[int]:
    if not token or len(token) != 32:
        return None

    try:
        pool = await get_db_pool()
        async with pool.acquire() as connection:
            result = await connection.fetchrow(
                token
            )
            if result:
                logger.info(f"Token validated for client_id: {result['client_id']}")
                return result['client_id']
            else:
                logger.warning(f"Invalid token provided: {token[:8]}...")
                return None

    except Exception as e:
        logger.error(f"Database error during token validation: {e}")
        return None

async def close_db_pool():
    global _db_pool
    if _db_pool:
        await _db_pool.close()
        _db_pool = None
        logger.info("Database connection pool closed")
