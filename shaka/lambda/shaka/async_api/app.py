import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from redis_client import get_redis_client
from auth import get_client_id_from_token

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
app = FastAPI(
    title="Async API",
    description="Long-polling async interface for SIM status events",
    version="1.0.0"
)

security = HTTPBearer()


async def authenticate_token(credentials: HTTPAuthorizationCredentials = Depends(security)) -> tuple[int, str]:
    token = credentials.credentials
    if not token or len(token) != 32:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token format"
        )

    client_id = await get_client_id_from_token(token)
    if client_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

    return client_id, token


@app.get("/api/v1/sim/event/status/{iccid}")
async def wait_for_sim_status(
    iccid: str,
    timeout: int = 30,
    auth: tuple[int, str] = Depends(authenticate_token)
):
    client_id, token = auth
    if timeout < 1 or timeout > 300:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Timeout must be between 1 and 300 seconds"
        )
    redis_client = get_redis_client()
    key = f"{client_id}:{token}:sim:{iccid}:status"
    try:
        logger.info(f"Waiting for status on key: {key} with timeout: {timeout}")
        result = redis_client.blpop(key, timeout=timeout)

        if result:
            _key, value = result
            logger.info(f"Received status update for {key}: {value}")
            return {"status": "ok", "data": value}
        else:
            logger.info(f"Timeout waiting for status on key: {key}")
            return {"status": "timeout"}

    except Exception as e:
        logger.error(f"Redis error for key {key}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Redis error: {str(e)}"
        )


@app.get("/health")
async def health_check():
    try:
        redis_client = get_redis_client()
        redis_client.ping()
        return {"status": "healthy", "redis": "connected"}
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service unhealthy: {str(e)}"
        )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
