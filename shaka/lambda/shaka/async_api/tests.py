import unittest
from unittest.mock import patch, MagicMock, AsyncMock
import asyncio
from fastapi.testclient import TestClient
from app import app
from redis_client import get_redis_client


class TestAsyncAPI(unittest.TestCase):

    def setUp(self):
        self.client = TestClient(app)

    @patch('app.get_redis_client')
    @patch('app.get_client_id_from_token')
    def test_timeout_scenario(self, mock_get_client_id, mock_redis):
        """Test the timeout scenario when no status is received."""
        # Mock authentication
        async def mock_auth_func(token):
            return 1
        mock_get_client_id.side_effect = mock_auth_func

        # Mock Redis BLPOP to return None (timeout)
        mock_redis_instance = MagicMock()
        mock_redis_instance.blpop.return_value = None
        mock_redis.return_value = mock_redis_instance

        # Make request with valid token
        response = self.client.get(
            "/api/v1/sim/event/status/test_iccid?timeout=1",
            headers={"Authorization": "Bearer valid_token_32_characters_long"}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "timeout"})

    @patch('app.get_redis_client')
    @patch('app.get_client_id_from_token')
    def test_successful_status_receive(self, mock_get_client_id, mock_redis):
        """Test successful status reception."""
        # Mock authentication
        async def mock_auth_func(token):
            return 1
        mock_get_client_id.side_effect = mock_auth_func

        # Mock Redis BLPOP to return a status
        mock_redis_instance = MagicMock()
        mock_redis_instance.blpop.return_value = ("1:valid_token_32_characters_long:sim:test_iccid:status", "installed")
        mock_redis.return_value = mock_redis_instance

        # Make request with valid token
        response = self.client.get(
            "/api/v1/sim/event/status/test_iccid?timeout=1",
            headers={"Authorization": "Bearer valid_token_32_characters_long"}
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "ok", "data": "installed"})

    @patch('app.get_client_id_from_token')
    def test_invalid_token(self, mock_get_client_id):
        """Test invalid token handling."""
        # Mock authentication to return None for invalid token
        mock_get_client_id.return_value = None

        response = self.client.get(
            "/api/v1/sim/event/status/test_iccid",
            headers={"Authorization": "Bearer invalid_token"}
        )

        self.assertEqual(response.status_code, 401)

    def test_missing_auth_header(self):
        """Test missing authorization header."""
        response = self.client.get("/api/v1/sim/event/status/test_iccid")
        self.assertEqual(response.status_code, 403)

    @patch('app.get_redis_client')
    def test_health_check(self, mock_redis):
        """Test health check endpoint."""
        mock_redis_instance = MagicMock()
        mock_redis_instance.ping.return_value = True
        mock_redis.return_value = mock_redis_instance

        response = self.client.get("/health")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {"status": "healthy", "redis": "connected"})


if __name__ == '__main__':
    unittest.main()
