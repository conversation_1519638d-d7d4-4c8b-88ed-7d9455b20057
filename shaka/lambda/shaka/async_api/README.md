# Async API

A lightweight FastAPI service for long-polling async interfaces, specifically designed for SIM status events.

## Overview

This service provides a long-polling API endpoint that waits for SIM status changes using Redis BLPOP operations. It's designed to handle the async nature of SIM installations without blocking server threads in the main nexus application.

## Features

- **Bearer Token Authentication**: Uses 32-character tokens from subscriber records
- **Redis-based Messaging**: Uses Redis BLPOP for efficient long-polling
- **Timeout Handling**: Configurable timeouts (1-300 seconds)
- **Health Checks**: Built-in health check endpoint
- **Error Handling**: Comprehensive error handling and logging

## API Endpoints

### GET /api/v1/sim/event/status/{iccid}

Wait for a SIM status event.

**Headers:**
- `Authorization: Bearer <32-char-token>`

**Parameters:**
- `iccid` (path): The SIM ICCID to monitor
- `timeout` (query, optional): Timeout in seconds (default: 30, max: 300)

**Response:**
```json
// Success
{"status": "ok", "data": "installed"}

// Timeout
{"status": "timeout"}
```

### GET /health

Health check endpoint.

**Response:**
```json
{"status": "healthy", "redis": "connected"}
```

## Environment Variables

- `REDIS_HOST`: Redis server host (default: localhost)
- `REDIS_PORT`: Redis server port (default: 6379)
- `DB_HOST`: Database host for token validation
- `DB_USER`: Database user
- `DB_PASSWORD`: Database password

## Redis Key Format

Status events are stored in Redis with the key format:
```
{client_id}:{token}:sim:{iccid}:status
```

## Development

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the server:
   ```bash
   uvicorn async_api.app:app --reload
   ```

3. Run tests:
   ```bash
   python -m unittest tests.py
   ```

4. Run linting:
   ```bash
   flake8 .
   ```

## Deployment

This service is designed for AWS App Runner deployment using the included Procfile.
