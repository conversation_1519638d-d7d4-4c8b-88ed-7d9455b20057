import { Button } from "@/components/Button";
import ErrorText from "@/components/ErrorText";
import { PaymentElement, useCheckout } from "@stripe/react-stripe-js";
import { useState } from "react";
import { useSubscriber } from "@/hooks/useSubscriber";
import { ConfirmError } from "@stripe/stripe-js";

export default function StripeChangeCardDetailsForm() {
  const { subscriber } = useSubscriber();
  const { confirm } = useCheckout();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<ConfirmError | null>(null);

  const handleSubmit = (event: { preventDefault: () => void }) => {
    event.preventDefault();
    setLoading(true);
    confirm({ email: subscriber?.email }).then((result) => {
      if (result.type === "error") {
        setError(result.error);
      }
      setLoading(false);
    });
  };

  return (
    <form onSubmit={handleSubmit} className="overflow-hidden">
      <PaymentElement />
      <div className="mt-6">
        {error && (
          <div className="mb-4">
            <ErrorText>{error.message}</ErrorText>
          </div>
        )}
      </div>
      <Button disabled={loading} loading={loading} variant="filled">
        {loading ? "Processing..." : "Change card details"}
      </Button>
    </form>
  );
}
