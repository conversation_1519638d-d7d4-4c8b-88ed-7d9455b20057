import { useEffect, useRef, useState } from 'react';

const calculateCubicBezierValue = (startX: number, startY: number, endX: number, endY: number, time: number) => {
  return 3 * (1 - time) * (1 - time) * time * startY + 3 * (1 - time) * time * time * endY + time * time * time;
};

function UseLogoAnimation(currentStep: number, duration = 300) {
  const targetFullness = currentStep ? (currentStep / 6) * 100 : 100;
  const [currentProgress, setCurrentProgress] = useState(0);
  const progressRef = useRef<HTMLDivElement | null>(null);
  const animationRef = useRef<number | null>(null);

  useEffect(() => {
    if (!progressRef.current) return;

    const startProgress = currentProgress;
    const startTime = performance.now();

    const animateProgress = (timestamp: number) => {
      const elapsed = timestamp - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easedProgress = calculateCubicBezierValue(0.16, 1, 0.3, 1, progress);

      // Calculate the current progress value
      const newProgress = startProgress + (targetFullness - startProgress) * easedProgress;

      // Update the CSS variable
      setCurrentProgress(newProgress);

      // Continue animation if not complete
      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animateProgress);
      }
    };

    // Start the animation
    animationRef.current = requestAnimationFrame(animateProgress);

    // Cleanup
    return () => {
      if (animationRef.current !== null) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [targetFullness, currentProgress, duration]);


  return {
    currentProgress,
    progressRef
  }
}

export default UseLogoAnimation;