# Generated by Django 4.2.7 on 2025-05-06 13:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0164_perk_redeem_details'),
    ]

    operations = [
        migrations.AddField(
            model_name='perkredemption',
            name='is_confirmed',
            field=models.BooleanField(default=True, help_text='Whether this redemption has been confirmed. Auto-confirmed if not required by client.'),
        ),
        migrations.AlterField(
            model_name='perkredemption',
            name='fulfilment_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('fulfilled', 'Fulfilled'), ('errored', 'Errored'), ('awaiting_confirmation', 'Awaiting Confirmation')], default='pending', max_length=25),
        ),
    ]
