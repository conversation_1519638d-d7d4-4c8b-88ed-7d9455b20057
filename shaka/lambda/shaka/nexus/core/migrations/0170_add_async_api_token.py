# Generated by Django 4.2.7 on 2025-05-29 12:41

import core.models
import secrets
import string
from django.db import migrations, models


def generate_unique_tokens(apps, schema_editor):
    Subscriber = apps.get_model('core', 'Subscriber')

    def generate_token():
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

    for subscriber in Subscriber.objects.all():
        token = generate_token()
        while Subscriber.objects.filter(async_api_token=token).exists():
            token = generate_token()
        subscriber.async_api_token = token
        subscriber.save(update_fields=['async_api_token'])

def reverse_generate_tokens(apps, schema_editor):
    Subscriber = apps.get_model('core', 'Subscriber')
    Subscriber.objects.update(async_api_token='')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0169_sim_esim_enabled_sim_esim_installed'),
    ]

    operations = [
        migrations.AddField(
            model_name='subscriber',
            name='async_api_token',
            field=models.CharField(default='', help_text='32-character token for async API authentication', max_length=32, blank=True),
        ),
        migrations.RunPython(generate_unique_tokens, reverse_generate_tokens),
        migrations.AlterField(
            model_name='subscriber',
            name='async_api_token',
            field=models.CharField(default=core.models.generate_async_api_token, help_text='32-character token for async API authentication', max_length=32, unique=True),
        ),
    ]
