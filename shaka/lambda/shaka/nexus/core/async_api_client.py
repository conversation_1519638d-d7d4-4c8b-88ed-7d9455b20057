import logging
import redis
from django.conf import settings

logger = logging.getLogger(__name__)
class SimStatusClient:    
    def __init__(self):
        self.redis_client = redis.Redis(
            host=getattr(settings, 'ASYNC_API_REDIS_HOST', 'localhost'),
            port=getattr(settings, 'ASYNC_API_REDIS_PORT', 6379),
            db=0,
            decode_responses=True
        )
    
    def set_sim_status(self, client_id, token, iccid, status):
        try:
            key = f"{client_id}:{token}:sim:{iccid}:status"
            result = self.redis_client.lpush(key, status)
            logger.info(f"Set SIM status for key {key}: {status}")
            return result > 0
        except Exception as e:
            logger.error(f"Failed to set SIM status for {client_id}:{token}:sim:{iccid}:status - {e}")
            return False
    
    def test_connection(self):
        try:
            return self.redis_client.ping()
        except Exception as e:
            logger.error(f"Redis connection test failed: {e}")
            return False
