import logging
import redis
from django.conf import settings


logger = logging.getLogger(__name__)


class SimStatusClient:
    """Client for communicating with the async_api Redis instance to set SIM status events."""
    
    def __init__(self):
        self.redis_client = redis.Redis(
            host=getattr(settings, 'ASYNC_API_REDIS_HOST', 'localhost'),
            port=getattr(settings, 'ASYNC_API_REDIS_PORT', 6379),
            db=0,
            decode_responses=True
        )
    
    def set_sim_status(self, client_id, token, iccid, status):
        """
        Set a SIM status event in Redis for the async API to pick up.
        
        Args:
            client_id (int): The client ID
            token (str): The subscriber's async API token
            iccid (str): The SIM ICCID
            status (str): The new status to set
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            key = f"{client_id}:{token}:sim:{iccid}:status"
            # Use LPUSH to add the status to the list (BLP<PERSON> will read from the left)
            result = self.redis_client.lpush(key, status)
            logger.info(f"Set SIM status for key {key}: {status}")
            return result > 0
        except Exception as e:
            logger.error(f"Failed to set SIM status for {client_id}:{token}:sim:{iccid}:status - {e}")
            return False
    
    def test_connection(self):
        """Test the Redis connection."""
        try:
            return self.redis_client.ping()
        except Exception as e:
            logger.error(f"Redis connection test failed: {e}")
            return False
