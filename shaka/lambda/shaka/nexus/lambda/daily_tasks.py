import logging
import os
import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(_, __):
    endpoints_and_tokens = [
        ('SIMP_POINTS_ENDPOINT', 'SIMP_API_TOKEN')
    ]

    for endpoint, token in endpoints_and_tokens:
        api_endpoint = os.environ[endpoint]
        api_token = os.environ[token]
        headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/x-www-form-urlencoded',
        }

        logger.info('Posting to %s', api_endpoint)
        response = requests.post(api_endpoint, data={}, headers=headers, timeout=60)
        response.raise_for_status()
