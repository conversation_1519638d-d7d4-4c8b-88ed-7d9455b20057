# Generated by Django 4.2.7 on 2025-05-11 23:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0167_pacrequest_created'),
        ('simp', '0013_subscriberspin'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='subscriberspin',
            options={'ordering': ['-id']},
        ),
        migrations.AlterUniqueTogether(
            name='subscriberspin',
            unique_together={('draw', 'subscriber')},
        ),
        migrations.AddIndex(
            model_name='subscriberspin',
            index=models.Index(fields=['draw', 'subscriber'], name='simp_subscr_draw_id_5f0f65_idx'),
        ),
    ]
