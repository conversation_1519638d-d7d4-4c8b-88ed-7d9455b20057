# Generated by Django 4.2.7 on 2025-05-15 13:16

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0167_pacrequest_created'),
        ('simp', '0014_alter_subscriberspin_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Spin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_assigned', models.DateTimeField(default=django.utils.timezone.now)),
                ('expiry_datetime', models.DateTimeField(blank=True, null=True)),
                ('prize', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='spin_used', to='simp.prize')),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='spins', to='core.subscriber')),
            ],
            options={
                'ordering': ['-date_assigned'],
                'indexes': [models.Index(fields=['subscriber'], name='simp_spin_subscri_a4e2fa_idx'), models.Index(fields=['expiry_datetime'], name='simp_spin_expiry__4e97af_idx')],
            },
        ),
    ]
