# Generated by Django 4.2.7 on 2025-05-08 02:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0166_subscriber_payment_method_reference'),
        ('simp', '0012_rename_simp_prize_draw_id_056025_idx_simp_prize_draw_id_ee2617_idx_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriberSpin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('spins_remaining', models.IntegerField(default=0)),
                ('draw', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriber_spins', to='simp.draw')),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='draw_spins', to='core.subscriber')),
            ],
        ),
    ]
