import withDrawer from 'src/hocs/withDrawer';
import { useClient } from 'src/hooks/useClient';

const VideoDrawerContent = () => {
  const { clientName } = useClient();

  return (
    <div className="px-5 -mb-5">
      <p className="mb-4">
        Follow the instructions below to install your eSIM. Label your new plan
        as “{clientName}”
      </p>
      <video
        src="/apple_installation_esim.mp4"
        autoPlay
        loop
        muted
        playsInline
        className="w-full object-cover pointer-events-none translate-y-1"
      >
        Your browser does not support the video tag.
      </video>
    </div>
  );
};

const VideoDrawer = withDrawer(VideoDrawerContent);

export default VideoDrawer;
