import { useState } from 'react';
import { AddUserIcon } from 'src/assets/icons/AddUser';
import Button from 'src/components/common/Button';
import WhiteBlock from 'src/components/common/WhiteBlock';
import ShareEsimDrawer from '../ShareEsimDrawer';
import { ShareEsimInputs } from 'src/schemas/share-sim';
import { shareEsim } from 'src/api/settings';
import { toast } from 'react-toastify';

export default function ShareEsim() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSimSharing = (data: ShareEsimInputs) => {
    shareEsim(data)
      .then(() => {
        setIsDrawerOpen(false);
        toast.success('eSim shared successfully!');
      })
      .catch((err) => {
        setError(
          err?.response?.data?.message ||
            'Something went wrong. Please try again later.'
        );
      });
  };

  return (
    <WhiteBlock>
      <div className="flex items-center gap-2.5 mb-4">
        <AddUserIcon className="w-4 h-4" />
        <span className="text-sm font-semibold">
          Is this eSIM not for your phone?
        </span>
      </div>
      <Button color="black" size="medium" onClick={() => setIsDrawerOpen(true)}>
        Share eSIM
      </Button>

      <ShareEsimDrawer
        isOpen={isDrawerOpen}
        onClose={() => setIsDrawerOpen(false)}
        title="Share eSIM"
        minHeightMobile="30dvh"
        onShareEsim={handleSimSharing}
        error={error}
      />
    </WhiteBlock>
  );
}
