export function AddUserIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 8 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clip-path="url(#clip0_2348_2629)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.57143 1.71429C4.57143 2.66106 3.80391 3.42857 2.85714 3.42857C1.91037 3.42857 1.14286 2.66106 1.14286 1.71429C1.14286 0.767514 1.91037 0 2.85714 0C3.80391 0 4.57143 0.767514 4.57143 1.71429ZM6.14286 4.28571C6.37954 4.28571 6.57143 4.47759 6.57143 4.71429V5.71429H7.57143C7.80811 5.71429 8 5.90617 8 6.14286C8 6.37954 7.80811 6.57143 7.57143 6.57143H6.57143V7.57143C6.57143 7.80811 6.37954 8 6.14286 8C5.90617 8 5.71429 7.80811 5.71429 7.57143V6.57143H4.71429C4.47759 6.57143 4.28571 6.37954 4.28571 6.14286C4.28571 5.90617 4.47759 5.71429 4.71429 5.71429H5.71429V4.71429C5.71429 4.47759 5.90617 4.28571 6.14286 4.28571ZM2.85714 3.99998C3.7105 3.99998 4.47647 4.37409 5 4.96725V4.99999H4.71429C4.0831 4.99999 3.57143 5.51167 3.57143 6.14286C3.57143 6.57314 3.80921 6.94789 4.16054 7.14286H0.285714C0.127919 7.14286 0 7.01491 0 6.85714C0 5.27917 1.27919 3.99998 2.85714 3.99998Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2348_2629">
          <rect width="8" height="8" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
