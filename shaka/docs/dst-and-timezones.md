
The bottom line is that relativedelta is misleading, and you should use our time_control classes or a slightly convoluted process described below instead. All of the code here uses this as a baseline:

```python
>>> from pytz import timezone as pytz_timezone
>>> from datetime import datetime, timedelta, timezone as py_timezone
>>> from dateutil.relativedelta import relativedelta
>>> uk_tz = pytz_timezone('Europe/London')
>>> def debug_dt(dt):
...   print(dt)
...   print(repr(dt))
...   print(dt.timestamp())
...
>>>

```

## Precursor

If you don't know what DST is, or don't know what a timezone is, it's worth looking those up. Timezones in particular have two meanings - there's the 'UTC offest' timezone (e.g. GMT, BST) which specifies what the time is relative to UTC, and then there's the locale timezone (e.g. Europe/London) which specifies what the daylight savings rules are. 

It would also be helpful to have a good knowledge of python's naive vs aware datetimes, and pytz's localize and astimezone methods.

## What is a day?

To understand the problem it's important to understand what is meant by 'a day later' (or earlier). If it's 2pm and I say 'a day later' I mean 2pm tomorrow. This is not 86400 seconds later. For example, in 2025 the clocks go back at 2am on the 26th of October. Here's midnight, so before the switch:

```python
>>> midnight_cfg_day = uk_tz.localize(datetime(2025,10,26,0,0,0))
>>> debug_dt(midnight_cfg_day)
2025-10-26 00:00:00+01:00
datetime.datetime(2025, 10, 26, 0, 0, tzinfo=<DstTzInfo 'Europe/London' BST+1:00:00 DST>)
1761433200.0
```

Ok looks good - notice it's BST (i.e. UTC+1). A day later than this should be midnight 27th of October. Let's add 86400 seconds

```python
>>> wrong_tomorrow = midnight_cfg_day + timedelta(seconds=86400)
>>> debug_dt(wrong_tomorrow)
2025-10-27 00:00:00+01:00
datetime.datetime(2025, 10, 27, 0, 0, tzinfo=<DstTzInfo 'Europe/London' BST+1:00:00 DST>)
1761519600.0
```

At first glance it looks good - midnight on the 27th. But notice the timezone - it's still BST! At that time the correct timezone is GMT for Europe/London. If we convert it:

```python
>>> debug_dt(wrong_tomorrow.astimezone(uk_tz))
2025-10-26 23:00:00+00:00
datetime.datetime(2025, 10, 26, 23, 0, tzinfo=<DstTzInfo 'Europe/London' GMT0:00:00 STD>)
1761519600.0
```

Oops - our 'day later' is actually the same day.

## Does relativedelta help?

The short answer is no, for not quite the same reason. Here's the same idea:

```python
>>> wrong_tomorrow = midnight_cfg_day + relativedelta(seconds=86400)
>>> debug_dt(wrong_tomorrow)
2025-10-27 00:00:00+01:00
datetime.datetime(2025, 10, 27, 0, 0, tzinfo=<DstTzInfo 'Europe/London' BST+1:00:00 DST>)
1761519600.0
```

Note the timezone and epoch timestamp are still wrong. Using days instead of seconds similarly doesn't help:

```python
>>> wrong_tomorrow = midnight_cfg_day + relativedelta(days=1)
>>> debug_dt(wrong_tomorrow)
2025-10-27 00:00:00+01:00
datetime.datetime(2025, 10, 27, 0, 0, tzinfo=<DstTzInfo 'Europe/London' BST+1:00:00 DST>)
1761519600.0
```

## What about months?

Same problem. 'A month later' should be the exact same day and time, if possible, but in the next month. So October 26th midnight should be November 26th midnight a month later, but:

```python
>>> wrong_next_month = midnight_cfg_day + relativedelta(months=1)
>>> debug_dt(wrong_next_month)
2025-11-26 00:00:00+01:00
datetime.datetime(2025, 11, 26, 0, 0, tzinfo=<DstTzInfo 'Europe/London' BST+1:00:00 DST>)
1764111600.0
```

Wrong timezone, and sure enough:

```python
>>> debug_dt(wrong_next_month.astimezone(uk_tz))
2025-11-25 23:00:00+00:00
datetime.datetime(2025, 11, 25, 23, 0, tzinfo=<DstTzInfo 'Europe/London' GMT0:00:00 STD>)
1764111600.0
```

## What's the cause?

The cause is that relativedelta understands months and the days in them but not locale timezones. If you ask it for one month later with an aware datetime it will always give you a response in the same timezone, which once converted to the correct UTC offset will be out by whatever the DST value is.

## What's the solution in general?

In my fairly extensive testing in Europe/London I find that relativedelta always gets the right time and day, just the wrong timezone, so although it feels like a sin, naivifying and awareifying the relativedelta value works correctly:

```python
>>> naive_next_month = wrong_next_month.replace(tzinfo=None)
>>> aware_next_month = uk_tz.localize(naive_next_month)
>>> debug_dt(aware_next_month)
2025-11-26 00:00:00+00:00
datetime.datetime(2025, 11, 26, 0, 0, tzinfo=<DstTzInfo 'Europe/London' GMT0:00:00 STD>)
1764115200.0
```

You can do this more manually if you don't trust it, by e.g. getting the individual date parts out and reconstructing the naive timestamp yourself but I don't think it matters.

## What if I want to add N seconds instead of 'days'?

Then you're fine but you aren't really caring about 'relative' dates. Adding N seconds can be thought of as increasing the epoch timestamp which always has a definitive time in a locale timezone.

## What about python's new timezones?

They're probably fine too with the same approach but we're using pytz because we're on an older version of python and django uses it too.
## What's the Shaka solution?

I've written the time_control classes that are extensively tested and do basically this transformation. They should be used for anything involve datetimes that might span DST boundaries.
## Conclusion

Relativedelta is basically useless if you might have a delta that spans DST boundaries.

Thank you for coming to my ted talk.
