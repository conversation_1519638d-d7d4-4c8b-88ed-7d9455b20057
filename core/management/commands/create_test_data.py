from django.core.management.base import BaseCommand
from django.utils import timezone
from core.models import Client, Sim, Msisdn, Service, PlanCode
from core.scenarios import SimScenarios, PacScenarios

class Command(BaseCommand):
    help = 'Creates test data for the fake telco service'

    def handle(self, *args, **kwargs):
        client, _ = Client.objects.get_or_create(
            name='Test Client',
            defaults={'api_key': 'test-api-key-123'}
        )
        self.stdout.write(self.style.SUCCESS(f'Created test client: {client.name}'))
        plan_codes = [
            ('UK_DATA_4GB', 4, '4GB Data Plan'),
            ('UK_DATA_6GB', 6, '6GB Data Plan'),
            ('UK_DATA_10GB', 10, '10GB Data Plan'),
            ('UK_DATA_20GB', 20, '20GB Data Plan'),
            ('UK_DATA_40GB', 40, '40GB Data Plan'),
            ('DATA_999', 100, 'Error Test Plan'),
        ]
        
        for code, data_limit, description in plan_codes:
            PlanCode.objects.get_or_create(
                code=code,
                defaults={
                    'data_limit_gb': data_limit,
                    'description': description
                }
            )
        self.stdout.write(self.style.SUCCESS('Created test plan codes'))
        
        # Create test SIMs
        test_sims = [
            (SimScenarios.NETWORK_ERROR_ICCID, 'Network Error Test SIM', 'inactive'),
            (SimScenarios.INVALID_SIM_ICCID, 'Invalid SIM Test', 'inactive'),
            (SimScenarios.TIMEOUT_ICCID, 'Timeout Test SIM', 'inactive'),
            (SimScenarios.PORT_ERROR_ICCID, 'Port Error Test SIM', 'inactive'),
            ('8944100000000123', 'Regular Test SIM 1', 'inactive'),
            ('8944100000000124', 'Regular Test SIM 2', 'active'),
            ('8944100000000125', 'Port-out Test SIM', 'active'),
        ]
        
        for iccid, description, status in test_sims:
            sim, created = Sim.objects.get_or_create(
                iccid=iccid,
                defaults={
                    'client': client,
                    'status': status
                }
            )
            if not created and sim.status != status:
                sim.status = status
                sim.save()
                
            if created or not Service.objects.filter(sim=sim, service_type='data').exists():
                Service.objects.create(
                    sim=sim,
                    service_type='data',
                    status='inactive' if status == 'inactive' else 'active'
                )
        self.stdout.write(self.style.SUCCESS('Created test SIMs'))
        
        # Create test MSISDNs
        test_msisdns = [
            ('447700900123', 'Regular Test Number 1', 'available', None),
            ('447700900124', 'Regular Test Number 2', 'active', '8944100000000124'),
            ('447700900125', 'Port-out Test Number', 'active', '8944100000000125'),
            ('447700900999', 'Port Error Test Number', 'available', None),
        ]
        
        for number, description, status, sim_iccid in test_msisdns:
            sim_obj = None
            if sim_iccid:
                sim_obj = Sim.objects.get(iccid=sim_iccid)
                
            msisdn, created = Msisdn.objects.get_or_create(
                number=number,
                defaults={
                    'status': status,
                    'sim': sim_obj
                }
            )
            
            if not created:
                msisdn.status = status
                msisdn.sim = sim_obj
                msisdn.save()
                
        self.stdout.write(self.style.SUCCESS('Created test MSISDNs'))

        self.stdout.write(self.style.SUCCESS('''
Test data created successfully!

Test Client:
- Name: Test Client
- API Key: test-api-key-123

Test Scenarios Available:
1. SIM Activation Tests:
   - Network Error: {network_error}
   - Invalid SIM: {invalid_sim}
   - Timeout: {timeout}

2. PAC Code Tests:
   - Invalid PAC: {invalid_pac}
   - Not Eligible: {not_eligible}
   - System Error: {system_error}
   - Already Ported: {already_ported}
   - Network Reject: {network_reject}

3. Regular Test SIMs:
   - 8944100000000123 (inactive)
   - 8944100000000124 (active with MSISDN 447700900124)
   - 8944100000000125 (active with MSISDN 447700900125 - for port-out testing)

4. Test MSISDNs:
   - 447700900123 (available)
   - 447700900124 (active, assigned to SIM 8944100000000124)
   - 447700900125 (active, assigned to SIM 8944100000000125 - for port-out testing)
   - 447700900999 (available, for port error testing)

5. Port-in/Port-out Testing:
   - Use PAC code 'PAC999' for successful port-in
   - Use SIM 8944100000000125 for port-out testing
   '''.format(
            network_error=SimScenarios.NETWORK_ERROR_ICCID,
            invalid_sim=SimScenarios.INVALID_SIM_ICCID,
            timeout=SimScenarios.TIMEOUT_ICCID,
            invalid_pac=PacScenarios.INVALID_PAC,
            not_eligible=PacScenarios.NOT_ELIGIBLE,
            system_error=PacScenarios.SYSTEM_ERROR,
            already_ported=PacScenarios.ALREADY_PORTED,
            network_reject=PacScenarios.NETWORK_REJECT,
        )))
