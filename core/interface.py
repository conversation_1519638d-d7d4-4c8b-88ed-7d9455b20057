import logging
from datetime import datetime, timedelta, timezone
from django.core.exceptions import ValidationError
from .models import Client, Sim, Msisdn, Service
from .scenarios import SimScenarios, PacScenarios, PlanScenarios, DataBarScenarios

logger = logging.getLogger(__name__)

class FakeTelcoInterface:
    def __init__(self, client):
        self.client = client

    def activate_sim(self, sim, plan_code, reference=None):
        try:
            sim_obj = Sim.objects.get(iccid=sim.serial_number, client=self.client)            
            error = SimScenarios.get_activation_error(sim.serial_number)
            if error:
                raise ValidationError(error)
            if sim_obj.status != 'inactive':
                raise ValidationError('SIM is not in inactive state')                
            sim_obj.status = 'activating'
            sim_obj.save()            
            Service.objects.get_or_create(
                sim=sim_obj,
                service_type='data',
                defaults={'status': 'inactive'}
            )
            
            return {
                'status': 'success',
                'message': f'SIM {sim.serial_number} activation initiated',
                'reference': reference
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {sim.serial_number} not found')

    def send_pac_request(self, sim, pac_code, msisdn, desired_date=None):
        try:
            sim_obj = Sim.objects.get(iccid=sim.serial_number, client=self.client)            
            error = PacScenarios.get_port_error(pac_code)
            if error:
                raise ValidationError(error)            
            msisdn_obj, created = Msisdn.objects.get_or_create(
                number=msisdn,
                defaults={
                    'status': 'reserved',
                    'sim': sim_obj,
                    'port_date': desired_date or (datetime.now(timezone.utc) + timedelta(days=2))
                }
            )
            
            if not created:
                msisdn_obj.status = 'reserved'
                msisdn_obj.sim = sim_obj
                msisdn_obj.port_date = desired_date or (datetime.now(timezone.utc) + timedelta(days=2))
                msisdn_obj.save()
            
            return {
                'status': 'success',
                'message': f'PAC request accepted for {msisdn}',
                'port_date': msisdn_obj.port_date.isoformat()
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {sim.serial_number} not found')

    def port_in(self, sim, pac_code, msisdn, desired_date=None):
        """
        Handle port-in request for a number to a SIM.
        
        For the happy case:
        1. Add the MSISDN to the system if it doesn't exist
        2. Assign it to the SIM
        3. Unassign old MSISDN from the SIM (if any) and mark it as unavailable
        """
        try:
            sim_obj = Sim.objects.get(iccid=sim.serial_number, client=self.client)
            
            # Check for errors based on PAC code
            error = PacScenarios.get_port_error(pac_code)
            if error:
                raise ValidationError(error)
            
            # Set port date (default to 2 days from now if not provided)
            port_date = desired_date or (datetime.now(timezone.utc) + timedelta(days=2))
            
            # Handle old MSISDN if the SIM already has one
            old_msisdn = Msisdn.objects.filter(sim=sim_obj).first()
            if old_msisdn:
                old_msisdn.sim = None
                old_msisdn.status = 'unavailable'
                old_msisdn.save()
            
            # Get or create the new MSISDN
            new_msisdn, created = Msisdn.objects.get_or_create(
                number=msisdn,
                defaults={
                    'status': 'active',
                    'sim': sim_obj,
                    'port_date': port_date
                }
            )
            
            if not created:
                new_msisdn.status = 'active'
                new_msisdn.sim = sim_obj
                new_msisdn.port_date = port_date
                new_msisdn.save()
            
            return {
                'status': 'success',
                'message': f'Number {msisdn} ported in to SIM {sim.serial_number}',
                'port_date': port_date.isoformat()
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {sim.serial_number} not found')

    def simulate_port_out(self, iccid):
        """
        Simulate a port-out for a SIM.
        
        1. Mark the SIM as terminated
        2. Unassign the MSISDN from the SIM
        3. Mark the MSISDN as unavailable
        """
        try:
            sim_obj = Sim.objects.get(iccid=iccid, client=self.client)
            
            # Mark SIM as terminated
            sim_obj.status = 'terminated'
            sim_obj.save()
            
            # Find associated MSISDN
            msisdn = Msisdn.objects.filter(sim=sim_obj).first()
            if msisdn:
                # Unassign MSISDN and mark as unavailable
                msisdn.sim = None
                msisdn.status = 'unavailable'
                msisdn.save()
                
                return {
                    'status': 'success',
                    'message': f'SIM {iccid} terminated and number {msisdn.number} ported out',
                    'msisdn': msisdn.number
                }
            else:
                return {
                    'status': 'success',
                    'message': f'SIM {iccid} terminated (no associated number)',
                }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {iccid} not found')

    def change_plan(self, sim, plan_code, fake=False):
        try:
            sim_obj = Sim.objects.get(iccid=sim.serial_number, client=self.client)            
            error = PlanScenarios.get_plan_error(plan_code)
            if error:
                raise ValidationError(error)
            
            if sim_obj.status != 'active':
                raise ValidationError('SIM must be active to change plan')
            
            return {
                'status': 'success',
                'message': f'Plan change to {plan_code} initiated',
                'effective_date': datetime.now(timezone.utc).isoformat()
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {sim.serial_number} not found')

    def bar_data(self, sim):
        try:
            sim_obj = Sim.objects.get(iccid=sim.serial_number, client=self.client)            
            error = DataBarScenarios.get_bar_error(sim.serial_number)
            if error:
                raise ValidationError(error)
            
            data_service = Service.objects.get(sim=sim_obj, service_type='data')
            if data_service.status == 'suspended':
                raise ValidationError('Data service already barred')
                
            data_service.status = 'suspended'
            data_service.save()
            
            return {
                'status': 'success',
                'message': f'Data barred for SIM {sim.serial_number}'
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {sim.serial_number} not found')
        except Service.DoesNotExist:
            raise ValidationError('Data service not found')

    def unbar_data(self, iccid, unbar_time):
        try:
            sim_obj = Sim.objects.get(iccid=iccid, client=self.client)            
            error = DataBarScenarios.get_bar_error(iccid)
            if error:
                raise ValidationError(error)
            
            data_service = Service.objects.get(sim=sim_obj, service_type='data')
            if data_service.status != 'suspended':
                raise ValidationError('Data service not currently barred')
                
            data_service.status = 'active'
            data_service.save()
            
            return {
                'status': 'success',
                'message': f'Data unbarred for SIM {iccid}',
                'effective_time': unbar_time.isoformat()
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {iccid} not found')
        except Service.DoesNotExist:
            raise ValidationError('Data service not found')

    def cancel_service(self, iccid):
        try:
            sim_obj = Sim.objects.get(iccid=iccid, client=self.client)
            
            sim_obj.status = 'cancelled'
            sim_obj.save()
            
            # Cancel all associated services
            Service.objects.filter(sim=sim_obj).update(status='cancelled')
            
            return {
                'status': 'success',
                'message': f'Services cancelled for SIM {iccid}'
            }
            
        except Sim.DoesNotExist:
            raise ValidationError(f'SIM {iccid} not found')
