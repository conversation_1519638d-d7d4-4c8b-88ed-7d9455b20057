from django.contrib import admin
from .models import Client, Sim, Msisdn, Service, PlanCode

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('name', 'api_key', 'created_at', 'updated_at')
    search_fields = ('name', 'api_key')

@admin.register(Sim)
class SimAdmin(admin.ModelAdmin):
    list_display = ('iccid', 'client', 'status', 'activation_date')
    list_filter = ('status', 'client')
    search_fields = ('iccid',)

@admin.register(Msisdn)
class MsisdnAdmin(admin.ModelAdmin):
    list_display = ('number', 'sim', 'status', 'port_date')
    list_filter = ('status',)
    search_fields = ('number',)

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('sim', 'service_type', 'status')
    list_filter = ('service_type', 'status')
    search_fields = ('sim__iccid',)

@admin.register(PlanCode)
class PlanCodeAdmin(admin.ModelAdmin):
    list_display = ('code', 'data_limit_gb', 'description')
    search_fields = ('code', 'description')
