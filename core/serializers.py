from rest_framework import serializers
from .models import C<PERSON>, Sim, Msisdn, Service, PlanCode

class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'name', 'api_key']

class SimSerializer(serializers.ModelSerializer):
    class Meta:
        model = Sim
        fields = ['id', 'client', 'iccid', 'status', 'activation_date']

class MsisdnSerializer(serializers.ModelSerializer):
    class Meta:
        model = Msisdn
        fields = ['id', 'number', 'sim', 'status', 'port_date']

class ServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = ['id', 'sim', 'service_type', 'status', 'settings']

class PlanCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanCode
        fields = ['id', 'code', 'data_limit_gb', 'description']

class SimActivationSerializer(serializers.Serializer):
    sim_serial = serializers.CharField()
    plan_code = serializers.CharField()
    reference = serializers.CharField(required=False)

class PacRequestSerializer(serializers.Serializer):
    sim_serial = serializers.CharField()
    pac_code = serializers.CharField()
    msisdn = serializers.CharField()
    desired_date = serializers.DateTimeField(required=False)

class PortInSerializer(serializers.Serializer):
    sim_serial = serializers.CharField()
    pac_code = serializers.CharField()
    msisdn = serializers.CharField()
    desired_date = serializers.DateTimeField(required=False)

class PlanChangeSerializer(serializers.Serializer):
    sim_serial = serializers.CharField()
    plan_code = serializers.CharField()
    fake = serializers.BooleanField(default=False)

class DataBarSerializer(serializers.Serializer):
    sim_serial = serializers.CharField()

class DataUnbarSerializer(serializers.Serializer):
    iccid = serializers.CharField()
    unbar_time = serializers.DateTimeField()

class ServiceCancellationSerializer(serializers.Serializer):
    iccid = serializers.CharField()
