class SimScenarios:
    NETWORK_ERROR_ICCID = '8944100000000999'
    INVALID_SIM_ICCID = '8944100000000888'
    TIMEOUT_ICCID = '8944100000000777'
    PORT_ERROR_ICCID = '8944100000000666'
    
    @classmethod
    def get_activation_error(cls, iccid):
        scenarios = {
            cls.NETWORK_ERROR_ICCID: 'Network currently unavailable',
            cls.INVALID_SIM_ICCID: 'Invalid SIM card',
            cls.TIMEOUT_ICCID: 'Request timed out'
        }
        return scenarios.get(iccid)
    
    @classmethod
    def get_port_error(cls, iccid):
        scenarios = {
            cls.PORT_ERROR_ICCID: 'Error processing port request'
        }
        return scenarios.get(iccid)

class PacScenarios:
    INVALID_PAC = 'ABC123'
    NOT_ELIGIBLE = 'XYZ789'
    SYSTEM_ERROR = 'ERR001'
    TIMEOUT_PAC = 'TMT001'
    ALREADY_PORTED = 'PRT001'
    NETWORK_REJECT = 'REJ001'
    
    @classmethod
    def get_port_error(cls, pac_code):
        scenarios = {
            cls.INVALID_PAC: 'Invalid PAC code provided',
            cls.NOT_ELIGIBLE: 'Number not eligible for porting',
            cls.SYSTEM_ERROR: 'System error occurred',
            cls.TIMEOUT_PAC: 'Request timed out',
            cls.ALREADY_PORTED: 'Number already ported',
            cls.NETWORK_REJECT: 'Port request rejected by network'
        }
        return scenarios.get(pac_code)

class PlanScenarios:
    UNAVAILABLE_PLAN = 'DATA_999'
    INVALID_PLAN = 'INVALID_101'
    ERROR_PLAN = 'ERROR_201'
    @classmethod
    def get_plan_error(cls, plan_code):
        scenarios = {
            cls.UNAVAILABLE_PLAN: 'Plan temporarily unavailable',
            cls.INVALID_PLAN: 'Invalid plan code',
            cls.ERROR_PLAN: 'Error processing plan change'
        }
        return scenarios.get(plan_code)

class DataBarScenarios:
    ALREADY_BARRED = '8944100000000666'
    BAR_ERROR = '8944100000000555'
    UNBAR_ERROR = '8944100000000444'
    @classmethod
    def get_bar_error(cls, iccid):
        scenarios = {
            cls.ALREADY_BARRED: 'Service already barred',
            cls.BAR_ERROR: 'Error barring service',
            cls.UNBAR_ERROR: 'Error unbarring service'
        }
        return scenarios.get(iccid)
