from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import Client, Sim, Msisdn, Service, PlanCode
from .scenarios import SimScenarios, PacScenarios

class ModelsTestCase(TestCase):
    def setUp(self):
        self.client_obj = Client.objects.create(
            name='Test Client',
            api_key='test-api-key-123'
        )
        self.sim = Sim.objects.create(
            client=self.client_obj,
            iccid='8944100000000123',
            status='inactive'
        )
        self.service = Service.objects.create(
            sim=self.sim,
            service_type='data',
            status='inactive'
        )
        self.msisdn = Msisdn.objects.create(
            number='447700900123',
            status='available'
        )
        self.plan_code = PlanCode.objects.create(
            code='UK_DATA_10GB',
            data_limit_gb=10,
            description='10GB Data Plan'
        )

    def test_sim_creation(self):
        self.assertEqual(self.sim.status, 'inactive')
        self.assertEqual(self.sim.client, self.client_obj)

    def test_service_creation(self):
        self.assertEqual(self.service.service_type, 'data')
        self.assertEqual(self.service.status, 'inactive')
        self.assertEqual(self.service.sim, self.sim)

    def test_msisdn_creation(self):
        self.assertEqual(self.msisdn.status, 'available')
        self.assertIsNone(self.msisdn.sim)

class APITestCase(APITestCase):
    def setUp(self):
        self.client_obj = Client.objects.create(
            name='Test Client',
            api_key='test-api-key-123'
        )
        self.sim = Sim.objects.create(
            client=self.client_obj,
            iccid='8944100000000123',
            status='inactive'
        )
        self.active_sim = Sim.objects.create(
            client=self.client_obj,
            iccid='8944100000000124',
            status='active'
        )
        self.error_sim = Sim.objects.create(
            client=self.client_obj,
            iccid=SimScenarios.NETWORK_ERROR_ICCID,
            status='inactive'
        )
        self.service = Service.objects.create(
            sim=self.sim,
            service_type='data',
            status='inactive'
        )
        self.msisdn = Msisdn.objects.create(
            number='447700900123',
            status='available'
        )
        self.active_msisdn = Msisdn.objects.create(
            number='447700900124',
            status='active',
            sim=self.active_sim
        )
        self.plan_code = PlanCode.objects.create(
            code='UK_DATA_10GB',
            data_limit_gb=10,
            description='10GB Data Plan'
        )
        self.client.credentials(HTTP_AUTHORIZATION='Bearer test-api-key-123')

    def test_authentication(self):
        response = self.client.post('/api/sims/activate/', {
            'sim_serial': '8944100000000123',
            'plan_code': 'UK_DATA_10GB'
        }, format='json')
        self.assertNotEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-key')
        response = self.client.post('/api/sims/activate/', {
            'sim_serial': '8944100000000123',
            'plan_code': 'UK_DATA_10GB'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_sim_activation_success(self):
        response = self.client.post('/api/sims/activate/', {
            'sim_serial': '8944100000000123',
            'plan_code': 'UK_DATA_10GB'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')

    def test_sim_activation_network_error(self):
        response = self.client.post('/api/sims/activate/', {
            'sim_serial': SimScenarios.NETWORK_ERROR_ICCID,
            'plan_code': 'UK_DATA_10GB'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Network currently unavailable', str(response.data['error']))

    def test_pac_request_invalid_code(self):
        response = self.client.post('/api/sims/request_pac/', {
            'sim_serial': '8944100000000123',
            'pac_code': PacScenarios.INVALID_PAC,
            'msisdn': '447700900123'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid PAC code', str(response.data['error']))

    def test_pac_request_success(self):
        response = self.client.post('/api/sims/request_pac/', {
            'sim_serial': '8944100000000123',
            'pac_code': 'PAC999',
            'msisdn': '447700900123'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('port_date', response.data)

    def test_port_in_success(self):
        response = self.client.post('/api/sims/port_in/', {
            'sim_serial': '8944100000000123',
            'pac_code': 'PAC999',
            'msisdn': '447700900125'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('port_date', response.data)
        
        # Verify MSISDN was created and assigned to SIM
        msisdn = Msisdn.objects.get(number='447700900125')
        self.assertEqual(msisdn.status, 'active')
        self.assertEqual(msisdn.sim.iccid, '8944100000000123')

    def test_port_in_invalid_pac(self):
        response = self.client.post('/api/sims/port_in/', {
            'sim_serial': '8944100000000123',
            'pac_code': PacScenarios.INVALID_PAC,
            'msisdn': '447700900125'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid PAC code', str(response.data['error']))

    def test_port_in_with_existing_msisdn(self):
        # First assign an MSISDN to the SIM
        self.msisdn.sim = self.sim
        self.msisdn.status = 'active'
        self.msisdn.save()
        
        # Now port in a new MSISDN
        response = self.client.post('/api/sims/port_in/', {
            'sim_serial': '8944100000000123',
            'pac_code': 'PAC999',
            'msisdn': '447700900125'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify old MSISDN was unassigned and marked unavailable
        self.msisdn.refresh_from_db()
        self.assertIsNone(self.msisdn.sim)
        self.assertEqual(self.msisdn.status, 'unavailable')
        
        # Verify new MSISDN was assigned
        new_msisdn = Msisdn.objects.get(number='447700900125')
        self.assertEqual(new_msisdn.sim.iccid, '8944100000000123')
        self.assertEqual(new_msisdn.status, 'active')

    def test_simulate_port_out(self):
        response = self.client.post(f'/api/sims/{self.active_sim.iccid}/simulate_port_out/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')
        
        # Verify SIM was marked as terminated
        self.active_sim.refresh_from_db()
        self.assertEqual(self.active_sim.status, 'terminated')
        
        # Verify MSISDN was unassigned and marked unavailable
        self.active_msisdn.refresh_from_db()
        self.assertIsNone(self.active_msisdn.sim)
        self.assertEqual(self.active_msisdn.status, 'unavailable')

    def test_bar_data_success(self):
        response = self.client.post('/api/sims/bar_data/', {
            'sim_serial': '8944100000000123'
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'success')

    def test_bar_data_error(self):
        response = self.client.post('/api/sims/bar_data/', {
            'sim_serial': SimScenarios.NETWORK_ERROR_ICCID
        }, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
