from django.db import models

class Client(models.Model):
    name = models.CharField(max_length=255)
    api_key = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

class Sim(models.Model):
    STATUS_CHOICES = [
        ('inactive', 'Inactive'),
        ('activating', 'Activating'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
        ('terminated', 'Terminated'),  # Added for port-out
    ]

    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    iccid = models.CharField(max_length=20, unique=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive')
    activation_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.iccid} ({self.status})"

class Msisdn(models.Model):
    STATUS_CHOICES = [
        ('available', 'Available'),
        ('reserved', 'Reserved'),
        ('active', 'Active'),
        ('porting_out', 'Porting Out'),
        ('ported_out', 'Ported Out'),
        ('unavailable', 'Unavailable'),  # Added for port-out
    ]

    number = models.CharField(max_length=15, unique=True)
    sim = models.ForeignKey(Sim, on_delete=models.SET_NULL, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='available')
    # pac_code field removed
    port_date = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.number} ({self.status})"

class Service(models.Model):
    TYPE_CHOICES = [
        ('voice', 'Voice'),
        ('data', 'Data'),
        ('sms', 'SMS'),
    ]

    STATUS_CHOICES = [
        ('inactive', 'Inactive'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
    ]

    sim = models.ForeignKey(Sim, on_delete=models.CASCADE)
    service_type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive')
    settings = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('sim', 'service_type')

    def __str__(self):
        return f"{self.sim.iccid} - {self.service_type} ({self.status})"

class PlanCode(models.Model):
    code = models.CharField(max_length=50, unique=True)
    data_limit_gb = models.IntegerField()
    description = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.code} ({self.data_limit_gb}GB)"
