from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import ValidationError
from .models import Client, Sim, Msisdn, Service, PlanCode
from .serializers import (
    ClientSerializer, SimSerializer, MsisdnSerializer, ServiceSerializer,
    PlanCodeSerializer, SimActivationSerializer, PacRequestSerializer,
    PlanChangeSerializer, DataBarSerializer, DataUnbarSerializer,
    ServiceCancellationSerializer, PortInSerializer
)
from .interface import FakeTelcoInterface
from .permissions import HasValidApiKey

class SimViewSet(viewsets.ModelViewSet):
    queryset = Sim.objects.all()
    serializer_class = SimSerializer
    permission_classes = [HasValidApiKey]

    @action(detail=False, methods=['post'])
    def activate(self, request):
        serializer = SimActivationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            sim = type('Sim', (), {'serial_number': serializer.validated_data['sim_serial']})
            interface = FakeTelcoInterface(request.user)
            
            result = interface.activate_sim(
                sim=sim,
                plan_code=serializer.validated_data['plan_code'],
                reference=serializer.validated_data.get('reference')
            )
            return Response(result, status=status.HTTP_200_OK)
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def request_pac(self, request):
        serializer = PacRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        try:
            sim = type('Sim', (), {'serial_number': serializer.validated_data['sim_serial']})
            interface = FakeTelcoInterface(request.user)
            result = interface.send_pac_request(
                sim=sim,
                pac_code=serializer.validated_data['pac_code'],
                msisdn=serializer.validated_data['msisdn'],
                desired_date=serializer.validated_data.get('desired_date')
            )
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def port_in(self, request):
        """
        Port in a number to a SIM using a PAC code.
        """
        serializer = PortInSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            sim = type('Sim', (), {'serial_number': serializer.validated_data['sim_serial']})
            interface = FakeTelcoInterface(request.user)
            
            result = interface.port_in(
                sim=sim,
                pac_code=serializer.validated_data['pac_code'],
                msisdn=serializer.validated_data['msisdn'],
                desired_date=serializer.validated_data.get('desired_date')
            )
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def simulate_port_out(self, request, pk=None):
        """
        Simulate a port-out for a SIM.
        Marks the SIM as terminated, unassigns the MSISDN, and marks the MSISDN as unavailable.
        """
        try:
            interface = FakeTelcoInterface(request.user)
            result = interface.simulate_port_out(pk)
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def change_plan(self, request):
        serializer = PlanChangeSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            sim = type('Sim', (), {'serial_number': serializer.validated_data['sim_serial']})
            interface = FakeTelcoInterface(request.user)
            
            result = interface.change_plan(
                sim=sim,
                plan_code=serializer.validated_data['plan_code'],
                fake=serializer.validated_data.get('fake', False)
            )
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def bar_data(self, request):
        serializer = DataBarSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            sim = type('Sim', (), {'serial_number': serializer.validated_data['sim_serial']})
            interface = FakeTelcoInterface(request.user)
            
            result = interface.bar_data(sim)
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def unbar_data(self, request):
        serializer = DataUnbarSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            interface = FakeTelcoInterface(request.user)
            
            result = interface.unbar_data(
                iccid=serializer.validated_data['iccid'],
                unbar_time=serializer.validated_data['unbar_time']
            )
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def cancel_service(self, request):
        serializer = ServiceCancellationSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        try:
            interface = FakeTelcoInterface(request.user)
            
            result = interface.cancel_service(serializer.validated_data['iccid'])
            return Response(result, status=status.HTTP_200_OK)
            
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

class ClientViewSet(viewsets.ModelViewSet):
    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    permission_classes = [HasValidApiKey]

class MsisdnViewSet(viewsets.ModelViewSet):
    queryset = Msisdn.objects.all()
    serializer_class = MsisdnSerializer
    permission_classes = [HasValidApiKey]

class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all()
    serializer_class = ServiceSerializer
    permission_classes = [HasValidApiKey]

class PlanCodeViewSet(viewsets.ModelViewSet):
    queryset = PlanCode.objects.all()
    serializer_class = PlanCodeSerializer
    permission_classes = [HasValidApiKey]
